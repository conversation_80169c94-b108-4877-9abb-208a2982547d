/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2023-04-02 12:12:52
 * @Description: 中医-数据中台-api
 */

import request from '@/utils/request'

const url = "/dataStatistics"
const url2 = "/org/clinic"

export default {
	// 奖金分配
	// list
	reqMoneyShareList: data =>
		request({
			url: `${url}/rewardListPage`,
			method: 'post',
			data
		}),

	// 审核
	reqCheckData: data =>
		request({
			url: `${url}/approvalReward`,
			method: 'post',
			data
		}),

	// 发放
	reqSendData: data =>
		request({
			url: `${url}/updateRewardStatus`,
			method: 'post',
			data
		}),

	// 资金结算
	// list
	reqMoneyList: data =>
		request({
			url: `${url}/settleListPage`,
			method: 'post',
			data
		}),

	// 审核
	reqMoneyCheck: data =>
		request({
			url: `${url}/approvalSettleStatus`,
			method: 'post',
			data
		}),

	// 结算
	reqMoneyData: data =>
		request({
			url: `${url}/updateSettleStatus`,
			method: 'post',
			data
		}),

	// 数据统计
	// 分店数据统计
	// list
	reqSingleList: data =>
		request({
			url: `${url}/dataStatisticsPage`,
			method: 'post',
			data
		}),
	// 今日营业额，会员总数，订单数 
	reqSingleStatisticsDetail: id =>
		request({
			url: `${url}/statisticsDetail?companyId=${id}`,
			method: 'get',
		}),

	// 会员消费趋势
	reqSingleStatisticsMemberConsume: (id, tollType) =>
		request({
			url: `${url}/memberConsumeDetail?companyId=${id}&tollType=${tollType}`,
			method: 'get',
		}),

	// 服务类型分布
	reqSingleServieType: (id) =>
		request({
			url: `${url}/memberServiceTypeDetail?companyId=${id}`,
			method: 'get',
		}),

	// 会员卡充值统计
	reqSingleRecharge: id =>
		request({
			url: `${url}/memberTopUpStatistics?companyId=${id}`,
			method: 'get',
		}),

	// 药品销售统计
	reqSingleSale: id =>
		request({
			url: `${url}/drugSaleStatistics?companyId=${id}`,
			method: 'get',
		}),

	// 消费明细
	reqSingleConsume: data =>
		request({
			url: `${url}/memberConsumeStatistics`,
			method: 'post',
			data
		}),

	// 分店导出
	reqSingleExport: data =>
		request({
			url: `${url}/exportConsumeDetail`,
			method: 'post',
			data,
			responseType: 'blob'
		}),

	// 客户数据统计
	// 客户数据统计：会员充值总额，会员总数  
	reqConsumeCustStatistics: data =>
		request({
			url: `${url}/custStatistics`,
			method: 'post',
			data
		}),

	// 充值金额
	reqConsumeCommonTopUpAmt: id =>
		request({
			url: `${url}/commonTopUpAmt?companyId=${id}`,
			method: 'get',
		}),

	// 客户年龄分布
	reqConsumeAge: id =>
		request({
			url: `${url}/memberAgeProp?companyId=${id}`,
			method: 'get',
		}),

	// 客户数据明细
	reqConsumeList: data =>
		request({
			url: `${url}/custDataDetail`,
			method: 'post',
			data,
		}),

	// 客户导出
	reqConsumeExport: data =>
		request({
			url: `${url}/exportCustDataDetail`,
			method: 'post',
			data,
			responseType: 'blob'
		}),

	// 消费记录
	reqConsumeRecord: data =>
		request({
			url: `${url}/memberConsumeTotalDetail`,
			method: 'post',
			data
		}),

	// 充值记录
	reqConsumeRechargeRecord: data =>
		request({
			url: `${url}/memberTotalTopUp`,
			method: 'post',
			data
		}),

	// 汇总统计
	// 营业额、会员数、结算总额、分配总额
	reqSummaryTotal: (id) =>
		request({
			url: `${url}/gatherFundTotal?companyId=${id}`,
			method: 'get'
		}),

	// 门店会员充值
	reqSummaryMember: id =>
		request({
			url: `${url}/memberPayStatistic?companyId=${id}`,
			method: 'get',
		}),

	// 门店消费
	reqSummarySale: id =>
		request({
			url: `${url}/salesStatistics?companyId=${id}`,
			method: 'get',
		}),

	// 总部收支流水
	reqSummaryFlowing: data =>
		request({
			url: `${url}/flowingCapitals`,
			method: 'post',
			data
		}),

	// 导出
	reqSummaryExport: data =>
		request({
			url: `${url}/flowingCapitalExport`,
			method: 'post',
			data,
			responseType: 'blob'
		}),

	// 账户管理
	// list
	reqAccountList: data =>
		request({
			url: `${url2}/list`,
			method: 'post',
			data
		}),
	// 新增
	reqAccountAdd: data =>
		request({
			url: `${url2}/add`,
			method: 'post',
			data
		}),
	// 详情
	reqAccountDetail: id =>
		request({
			url: `${url2}/${id}`,
			method: 'get',
		}),
	// 删除
	reqAccountRemove: data =>
		request({
			url: `${url2}/delete`,
			method: 'post',
			data
		}),
	// 父级id
	reqAccountParentId: (id) =>
		request({
			url: `${url}/parentId?companyId=${id}`,
			method: 'get',
		}),

	// 退卡结算
	// list
	reqBackList: data =>
		request({
			url: `${url}/memberReturnCard`,
			method: 'post',
			data
		}),
	// 详情
	reqBackDetail: id =>
		request({
			url: `${url}/memberReturnCardDetail?memberId=${id}`,
			method: 'get',
		}),
	// 审核
	reqBackCheck: id =>
		request({
			url: `${url}/updateSettleApproval?memberId=${id}`,
			method: 'get',
		}),
	// 总公司给用户结算
	reqBackConsumeData: (id) =>
		request({
			url: `${url}/returnSettleApproval?memberId=${id}`,
			method: 'get',
		}),
	// 分公司给总公司结算
	reqBackHeadData: (id, companyId) =>
		request({
			url: `${url}/companyReturnSettle?memberId=${id}&companyId=${companyId}`,
			method: 'get',
		}),

};