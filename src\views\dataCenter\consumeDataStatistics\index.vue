<!--
 * @Author: xiang<PERSON>i
 * @Date: 2025-06-16 14:03:21
 * @Description: 中医-数据中台-数据统计-客户数据统计
 * @type: page
-->

<template>
	<div>
		<el-card class="page-container">
			<div class="homeHeader">
				<template v-for="item in dataList">
					<el-card class="homeTitleAndNum">
						<div class="title">
							<div v-text="item.label"></div>
						</div>
						<div class="num">
							<div v-text="item.num"></div>
						</div>
					</el-card>
				</template>
			</div>

			<div class="homeContent">
				<el-card class="item itemList">
					<h3 class="text">常见充值金额</h3>
					<EchartComp ref="houseStatus" class="homeEcharts0" :options="options0" style="width:290px;height:290px;">
					</EchartComp>
					<div class="homeList">
						<ul>
							<li v-for="(item, index) in chartData.children" :key="index">
								<span class="range">{{ item.name }}:</span>
								<span class="percent">{{ item.value }}%</span>
								<span class="bar"></span>
							</li>
						</ul>
					</div>

				</el-card>
				<el-card class="item">
					<h3 class="text">客户年龄分布</h3>
					<EchartComp ref="houseStatus" class="homeEcharts1" :options="options1"></EchartComp>
				</el-card>
			</div>
			<div class="homeContent2">
				<el-card class="item">
					<h3 class="text">客户数据明细</h3>
					<el-button class="export" size="mini" icon="el-icon-download" @click="handleExport">导出Excel</el-button>
					<Table class="tableStyle" :tableData="table.tableList" :columns="table.columns" :total="table.total"
						:pagination="table.pagination" :loading="table.isTableLoading" :sizeChange="handleSizeChange"
						:currentChange="handleCurrentChange" />
				</el-card>
			</div>

		</el-card>

		<!-- 充值消费记录弹框 -->
		<RechargeConsumeRecord ref="rechargeConsumeRecord" />
	</div>
</template>

<script>

import EchartComp from "../components/echartComp";
import Table from "@/components/Combinecom/table";
import Options from '../options'
import { handlePayTypeText } from '@/utils'
import RechargeConsumeRecord from './rechargeConsumeRecord'
import { handleFormatMoney } from '@/utils/validate'
import API from "@/api/dataCenter";
import { downloadExcel } from '@/utils'

export default {
	name: "detail",
	components: {
		EchartComp, Table, RechargeConsumeRecord
	},
	data() {
		return {
			dataList: [
				{
					label: "充值金额(元)",
					num: 0,
					value: "totalTopUpAmt",
					isLoading: true,
				},
				{
					label: "会员数(个)",
					num: 0,
					value: "memberCount",
					isLoading: true,
				},
			],
			options0: {}, // 折线图配置  会员消费
			options1: {}, // 饼图配置    服务类型
			chartData: {
				children: []
			},
			chartData2: [],
			colors: ['#048DB8', '#10C8B8', '#FF6B81'],
			// 表数据
			table: {
				// table参数
				total: 0,
				tableList: [],
				pagination: {
					limit: 20,
					offset: 0
				},
				columns: [
					{
						label: "序号",
						type: "index",
					},
					{
						label: "会员",
						prop: "memberName"
					},
					{
						label: "服务类型",
						prop: "serviceType",
					},
					{
						label: "金额(元)",
						prop: "consumeAmt",
						formatter: row => {
							return handleFormatMoney(row.consumeAmt)
						}
					},
					{
						label: "支付方式",
						prop: "payType",

					},
					{
						label: '操作',
						minWidth: 120,
						buttons: [
							{
								label: '消费记录',
								type: 'text',
								click: row => this.handleCheckRecord(1, row),
							},
							{
								label: '充值记录',
								type: 'text',
								click: row => this.handleCheckRecord(2, row),
							},
						]
					}
				],
				isTableLoading: false // 表格loading
			},

		};
	},
	mounted() {
		this.getAllStatistics();
	},
	methods: {
		// 充值消费记录弹框
		handleCheckRecord(type, row) {
			this.$refs.rechargeConsumeRecord.$emit(
				"openRecordDialog",
				type,
				row
			);
		},
		// total
		handleSizeChange(val) {
			this.table.pagination.offset = val;
			this.getTableData();
		},
		// 改变页数
		handleCurrentChange(val) {
			this.table.pagination.offset = val;
			this.getTableData();
		},
		// 配置图表
		handleConfigEcharts() {
			this.options0 = {
				tooltip: {
					formatter: function (info) {
						const value = info.value;
						const treePathInfo = info.treePathInfo;
						const treePath = [];

						for (let i = 1; i < treePathInfo.length; i++) {
							treePath.push(treePathInfo[i].name);
						}

						return [
							'<div style="font-size:14px;color:#fff;font-weight:400;line-height:26px;">',
							treePath.join(' > '),
							'</div>',
							'<div style="font-size:14px;color:#fff;font-weight:400;line-height:26px;">',
							'占比: ' + value + '%',
							'</div>'
						].join('');
					}
				},
				series: [{
					type: 'treemap',
					visibleMin: 1,
					data: this.chartData.children,
					breadcrumb: {
						show: false
					},
					label: {
						show: true,
						formatter: '{b}\n{c}%',
						fontSize: 12,
						color: '#fff'
					},
					upperLabel: {
						show: false,
					},
					itemStyle: {
						borderColor: '#5E737E',
						gapWidth: 1,  // 设置为0去除间隙
						borderWidth: 1  // 去除边框
					},
					levels: [
						{
							itemStyle: {
								borderWidth: 1,
								gapWidth: 0
							}
						},
						{
							itemStyle: {
								borderWidth: 1,
								gapWidth: 0
							}
						},
						{
							colorSaturation: [0.35, 0.5],
							itemStyle: {
								borderWidth: 1,
								gapWidth: 0,
								borderColorSaturation: 0.6
							}
						}
					]
				}],
				color: ['#277C90', '#11CBB4', '#F5B43C', '#EF8B59', '#E65C75', '#696BB5', '#0E488D', '#323143']
			}
			this.options1 = {
				tooltip: {
					trigger: 'item',
					formatter: '{a} <br/>{b}: {c} ({d}%)'
				},
				legend: {
					orient: 'vertical',
					left: 'left',
					top: '80',
					data: this.chartData2.map(item => item.name)
				},
				toolbox: {
					show: true,
				},
				series: [
					{
						name: '年龄分布',
						type: 'pie',
						radius: [30, 100],
						center: ['50%', '55%'],
						avoidLabelOverlap: false,
						roseType: 'area',
						itemStyle: {
							borderRadius: 8,
						},
						label: {
							show: false,
							position: 'center'
						},
						labelLine: {
							show: false
						},
						data: this.chartData2,
						// 自定义颜色
						color: ['#5087EC', '#68BBC4', '#58A55C', '#F2BD42', '#EE752F']
					}
				]
			};
		},

		// 导出
		async handleExport() {
			const data = {
				companyId: currentUser.company.id,
				limit: this.table.pagination.limit,
				offset: this.table.pagination.offset,
			}
			this.isExportLoading = true

			try {
				const res = await API.reqConsumeExport(data)
				// console.log('res', res)
				downloadExcel(res, '客户数据明细.xlsx')

				// console.log('res', res)
				this.$message.success('导出成功!')
				this.isExportLoading = false
			} catch (error) {
				this.isExportLoading = false
				this.$message.error(error)
			}
		},

		// 图表API
		async getAllStatistics() {
			Promise.all([
				this.getConsumeTotal(),
				this.getConsumeRecharge(),
				this.getConsumeMember(),
				this.getTableData()
			])
				.then(() => {
					this.handleConfigEcharts();
				})
				.catch((err) => {
					this.$resetMessage.warning(err);
				});
		},

		// 充值金额，会员数
		async getConsumeTotal() {
			const data = {
				companyId: currentUser.company.id
			}

			try {
				const res = await API.reqConsumeCustStatistics(data)
				// console.log('id', res)
				if (res.code === '100') {
					this.dataList.map(v => {
						for (let i in res.data) {
							if (v.value === i) {
								if (i === 'memberCount') {
									v.num = res.data[i]
								} else {
									v.num = handleFormatMoney(res.data[i])
								}
							}

						}
					})
				} else {
					this.$message.warning(res.msg)
				}
			} catch (error) {
				this.$message.error(error)
			}
		},

		// 充值金额
		async getConsumeRecharge() {
			try {
				const res = await API.reqConsumeCommonTopUpAmt(currentUser.company.id)
				// console.log('id', res)
				if (res.code === '100') {
					res.data.list.map(v => {
						this.chartData.children.push({
							name: v.moneyRange,
							value: v.memberCount,
						})
					})
				} else {
					this.$message.warning(res.msg)
				}
			} catch (error) {
				this.$message.error(error)
			}
		},

		// 客户年龄分布
		async getConsumeMember() {
			try {
				const res = await API.reqConsumeAge(currentUser.company.id)
				// console.log('id', res)
				if (res.code === '100') {
					if (res.data.length) {
						res.data.reverse().map(v => {
							this.chartData2.push({
								name: v.ageProp,
								value: v.memberCount

							})
						})
					}

				} else {
					this.$message.warning(res.msg)
				}
			} catch (error) {
				this.$message.error(error)
			}
		},

		// table数据
		async getTableData() {
			const data = {
				companyId: currentUser.company.id,
				// companyId: "2647273530380673139",
				limit: this.table.pagination.limit,
				offset: this.table.pagination.offset ? (this.table.pagination.offset - 1) * this.table.pagination.limit : 0,
			};
			try {
				this.table.isTableLoading = true;
				const res = await API.reqConsumeList(data);
				// console.log('res', res)
				if (res.code === '100') {
					if (res.data) {
						this.table.tableList = res.data.rows || [];
						this.table.total = Number(res.data.total);
					}

				} else {
					this.$message.warning(res.msg);
				}
				this.table.isTableLoading = false;
			} catch (error) {
				this.table.isTableLoading = false;
				// this.$message.warning(error);
				this.$message.error(error);
			}
		},

	},
};
</script>

<style lang="scss" scoped>
.HomeBackBtn {
	position: relative;
	top: -20px;
}

.homeHeader {
	display: flex;
	font-size: 16px;

	.homeTitleAndNum {
		width: 50%;
		margin-right: 10px;

		.title {
			font-size: 16px;
			color: #6C6C6C;
		}

		.num {
			font-size: 28px;
			color: #000000;
		}
	}
}

.homeHeader .homeTitleAndNum:last-child {
	margin-right: 0;
}


.homeContent {
	box-sizing: content-box;
	display: flex;
	margin-top: 20px;

	.item {
		width: 50%;
		margin-right: 10px;
		height: 320px;

		.text {
			font-size: 16px;
			color: #333;
			margin: 0;
		}

		/deep/ .el-card__body {
			height: 270px;
		}

	}
}

.homeContent .item:last-child {
	margin-right: 0;
}


.homeContent2 {
	box-sizing: border-box;
	display: flex;
	margin-top: 20px;
	width: 100%;

	.item {
		width: 100%;

		.text {
			display: inline-block;
			font-size: 16px;
			color: #333;
			margin: 0;
		}

		.export {
			float: right;
		}
	}

	.tableStyle {
		margin-top: 20px;
	}
}


.itemList {
	position: relative;

	.homeList {
		position: absolute;
		padding: 5px;
		margin-left: 20px;
		right: 0;
		bottom: 10px;

		ul {
			list-style: none;
			padding: 0;
			margin: 0;

			li {
				font-size: 12px;

				.range {
					display: inline-block;
					font-weight: bold;
				}

				.percent {
					display: inline-block;
					width: 50px;
					text-align: left;
				}

				.bar {
					display: inline-block;
					margin-left: 5px;
					vertical-align: middle;
				}
			}
		}

	}
}
</style>