{"name": "jeeke-client", "version": "1.0.0", "description": "jeeke-demo web client", "author": "Lining", "private": true, "scripts": {"dev": "webpack-dev-server --inline --progress --config build/webpack.dev.conf.js", "start": "npm run dev", "unit": "jest --config test/unit/jest.conf.js --coverage", "e2e": "node test/e2e/runner.js", "testting": "npm run unit && npm run e2e", "build": "node build/build.js", "test": "node build/test.js", "demo": "node build/demo.js"}, "dependencies": {"@bpmn-io/properties-panel": "^3.15.0", "axios": "^0.27.2", "bignumber.js": "^9.0.2", "bpmn-js": "^2.5.1", "bpmn-js-properties-panel": "^0.33.1", "camunda-bpmn-moddle": "^3.0.0", "default-passive-events": "^1.0.10", "echarts": "^4.1.0", "el-tree-transfer": "^2.4.7", "element-china-area-data": "^6.1.0", "element-ui": "^2.15.14", "file-saver": "^2.0.5", "fit2cloud-ui": "^1.5.4", "html2canvas": "1.0.0-rc.7", "js-base64": "^3.7.2", "js-cookie": "^3.0.1", "js-pinyin": "^0.1.9", "luckyexcel": "^1.0.1", "moment": "^2.29.4", "number-precision": "^1.5.2", "qrcodejs2": "0.0.2", "qs": "^6.5.1", "sortablejs": "^1.14.0", "v-distpicker": "^1.2.13", "vue-cropper": "^0.5.8", "vue-currency-input": "^1.22.6", "vue-grid-layout": "2.3.8", "vue-quill-editor": "^3.0.6", "vue-uuid": "^2.0.2", "vuedraggable": "^2.24.3", "xlsx": "^0.14.5", "xlsx-style": "^0.8.13"}, "devDependencies": {"autoprefixer": "^7.1.2", "babel-core": "^6.22.1", "babel-jest": "^21.0.2", "babel-loader": "^7.1.1", "babel-plugin-dynamic-import-node": "^1.2.0", "babel-plugin-transform-es2015-modules-commonjs": "^6.26.0", "babel-plugin-transform-runtime": "^6.22.0", "babel-polyfill": "^6.26.0", "babel-preset-env": "^1.3.2", "babel-preset-stage-2": "^6.22.0", "babel-register": "^6.22.0", "chalk": "^2.0.1", "chromedriver": "^2.27.2", "copy-webpack-plugin": "^4.0.1", "cross-spawn": "^5.0.1", "css-loader": "^0.28.0", "element-theme-chalk": "^2.15.3", "eventsource-polyfill": "^0.9.6", "extract-text-webpack-plugin": "^3.0.0", "file-loader": "^1.1.4", "friendly-errors-webpack-plugin": "^1.6.1", "html-webpack-plugin": "^2.30.1", "jest": "^21.2.0", "jest-serializer-vue": "^0.3.0", "nightwatch": "^0.9.12", "node-notifier": "^5.1.2", "node-sass": "^4.14.0", "optimize-css-assets-webpack-plugin": "^3.2.0", "ora": "^1.2.0", "portfinder": "^1.0.13", "postcss-import": "^11.0.0", "postcss-loader": "^2.0.8", "postcss-url": "^7.2.1", "quill": "^1.3.7", "rimraf": "^2.6.0", "sass-loader": "^6.0.6", "script-loader": "^0.7.2", "selenium-server": "^3.0.1", "semver": "^5.3.0", "shelljs": "^0.8.5", "svg-sprite-loader": "^6.0.9", "uglifyjs-webpack-plugin": "^1.1.1", "url-loader": "^0.5.8", "vue-jest": "^1.0.2", "vue-loader": "^13.3.0", "vue-style-loader": "^3.0.1", "vue-template-compiler": "^2.5.2", "webpack": "^3.6.0", "webpack-bundle-analyzer": "^2.9.0", "webpack-dev-server": "^2.9.1", "webpack-merge": "^4.1.0"}, "engines": {"node": ">= 4.0.0", "npm": ">= 3.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 8"]}