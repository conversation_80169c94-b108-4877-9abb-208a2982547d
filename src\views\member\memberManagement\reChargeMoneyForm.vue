<!--
 * @Author: x<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-11 10:51:11
 * @Description: 中医馆-更多-会员管理-充值弹框
 * @type: component
-->

<template>
	<el-dialog class="reChargeMoneyForm" :title="dialogProps.title" :visible.sync="dialogProps.visible"
		:close-on-click-modal="false" width="60%" @open="handleDialogOpen" append-to-body>
		<div>
			<el-form :model="formData" :rules="formRules" ref="reChargeMoneyForm" label-width="120px" label-position="right"
				class="edit-form">
				<el-row>
					<el-col :span="12">
						<el-form-item label="充值方式">
							<div>
								<el-radio-group v-model="formData.type" class="rechangeType">
									<el-radio v-for="item in rechangeTypeList" :key="item.value" :label="item.value">
										{{ item.name }}
									</el-radio>

								</el-radio-group>
							</div>
						</el-form-item>
					</el-col>
				</el-row>
				<el-row>
					<el-col :span="12">
						<el-form-item label="充值金额(元)" prop="money">
							<el-input step="any" style="width:300px;" v-model="formData.money" placeholder="请输入充值金额" type="number"
								min="0"
								onkeypress="return event.charCode >= 48 && event.charCode <= 57 || event.charCode === 46"></el-input>
						</el-form-item>
					</el-col>
				</el-row>
				<div class="tipContainer">
					<div class="tip tipWidth">tip：请充值特定数值的金额,充值赠送规则如下</div>
					<div class="tip">充1W赠1K</div>
					<div class="tip">充2W赠2.2K</div>
					<div class="tip">充3W赠3.6K</div>
					<div class="tip">充5W赠6.5K</div>
					<div class="tip">充8W赠10K</div>
					<div class="tip">充10W赠16K</div>
				</div>
			</el-form>

			<div slot="footer" class='dialog-footer'>
				<el-button type="primary" :plain='true' v-loading="isBtnLoading" @click="handleReCharge">确认充值</el-button>
				<el-button @click="dialogProps.visible = false">取 消</el-button>
			</div>
		</div>
	</el-dialog>
</template>

<script>
import API from "@/api/reChargeMoney";
import { listDictItemAll } from "@/api/sys/dictItem";

export default {
	name: "reChage",
	data() {
		return {
			dialogProps: {
				visible: false,
				title: '充值',
			},
			formData: {
				type: "payType_0"
			},
			formRules: {
				money: [
					{ required: true, message: "请输入充值金额", trigger: ["blur", "change"] },
				],
				// rechangeType: [
				// 	{ required: true, message: "请选择充值方式", trigger: "blur" },
				// ],
			},
			// 充值方式
			rechangeTypeList: [],

			isBtnLoading: false // 确认按钮loading
		};
	},
	mounted: async function () {
		this.$nextTick(() => {
			this.$on("openReChargeDialog", function (row) {
				// Object.assign(this.formData, ...row)
				// console.log('row', row)
				// console.log('formData', this.formData)

				this.formData.companyId = currentUser.company.id
				this.formData.memberManagementId = row.id
				this.formData.memberSetId = row.memberSet.id
				this.formData.card = row.card
				this.formData.createBy = row.createBy
				this.dialogProps.title = "充值";
				this.dialogProps.visible = true;

				this.getPayTypeList()
			})
		})
	},
	methods: {
		// 关闭弹窗
		handleCloseBtn() {
			this.dialogProps.visible = false;
			this.$refs["reChargeMoneyForm"].resetFields(); // 重置表单
			this.$emit("closeDialog");
		},
		// 确认充值
		handleReCharge() {
			this.$refs.reChargeMoneyForm.validate(async valid => {
				if (!valid) return

				const params = { ...this.formData }
				// console.log('params', params)
				// return
				try {
					let res = await API.reqReChargeManage(params)
					this.isBtnLoading = true;
					// console.log('res', res)
					if (res.code === '100') {
						this.$message({
							type: 'success',
							message: '充值成功！'
						})
						this.handleCloseBtn();
					} else {
						this.$message.warning(res.msg)
					}
					this.isBtnLoading = false;
				} catch (error) {
					this.isBtnLoading = false;
					this.$message({
						type: 'error',
						message: res.msg
					})
				}
			})
		},
		// 打开弹框初始化
		handleDialogOpen() {
			this.$nextTick(() => {
				if (this.$refs["reChargeMoneyForm"]) {
					this.$refs["reChargeMoneyForm"].clearValidate();
				}
			});
		},
		// 获取充值列表
		getPayTypeList() {
			let payType_search = {
				params: [
					{
						columnName: "dict_type_id",
						queryType: "=",
						value: "1008793465990693120",
					},
				],
			};
			payType_search.params.push.apply(payType_search.params, []);

			listDictItemAll(payType_search).then((res) => {
				// console.log('res', res)
				this.rechangeTypeList = res.data.slice(0, -2);;
				this.formData.type = this.rechangeTypeList[0].value
			});
		}
	},
};
</script>

<style lang="scss" scoped>
// tip框
.tipContainer {
	padding-left: 120px;
	padding-top: 5px;


	.tip {
		color: #000;
		line-height: 20px;
	}

	.tipWidth {
		width: 300px;
	}
}

.rechangeType {
	display: flex;
	margin-top: 10px;
}

.dialog-footer {
	float: right;
}
</style>