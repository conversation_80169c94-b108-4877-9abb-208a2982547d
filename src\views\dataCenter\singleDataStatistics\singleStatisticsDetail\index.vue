<!--
 * @Author: x<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-16 14:03:21
 * @Description: 中医-数据中台-数据统计-分店数据统计-详情
 * @type: page
-->

<template>
  <div>
    <el-card class="page-container">
      <el-button
        size="small"
        icon="el-icon-back"
        class="HomeBackBtn"
        @click="handleBack()"
        >返回</el-button
      >
      <div class="homeHeader">
        <template v-for="item in dataList">
          <el-card class="homeTitleAndNum">
            <div class="title">
              <div v-text="item.label"></div>
            </div>
            <div class="num">
              <div v-text="item.num"></div>
            </div>
          </el-card>
        </template>
      </div>

      <div class="homeContent">
        <el-card class="item itemStyle">
          <h3 class="text">会员消费趋势</h3>
          <EchartComp
            ref="houseStatus"
            class="homeEcharts0"
            :options="options0"
          ></EchartComp>
          <!-- <el-select class='selectStyle' v-model="selectedOption" placeholder="请选择消费类型" @change="handleChangeOptions">
						<el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
						</el-option>
					</el-select> -->
        </el-card>
        <el-card class="item">
          <h3 class="text">会员服务类型分布</h3>
          <EchartComp
            ref="houseStatus"
            class="homeEcharts1"
            :options="options1"
          ></EchartComp>
        </el-card>
      </div>
      <div class="homeContent2">
        <el-card class="item">
          <h3 class="text">会员卡充值记录</h3>
          <EchartComp
            ref="houseStatus"
            class="homeEcharts2"
            :options="options2"
          ></EchartComp>
        </el-card>
        <el-card class="item">
          <h3 class="text">药瓶销售TOP10</h3>
          <EchartComp
            ref="houseStatus"
            class="homeEcharts3"
            :options="options3"
          ></EchartComp>
        </el-card>
      </div>

      <div class="homeContent2-5">
        <Form
          class="demo-form-inline"
          ref="searchForm"
          :inline="true"
          :model="searchForm"
          :items="searchFormItems"
          :buttons="operationBtns"
          @search="handleSearch"
        />
      </div>

      <div class="homeContent3">
        <el-card class="item">
          <h3 class="text">分店消费明细</h3>
          <el-button
            class="export"
            size="mini"
            icon="el-icon-download"
            :loading="isExportLoading"
            @click="handleExport"
            >导出Excel</el-button
          >
          <Table
            class="tableStyle"
            :tableData="table.tableList"
            :columns="table.columns"
            :total="table.total"
            :pagination="table.pagination"
            :loading="table.isTableLoading"
            :sizeChange="handleSizeChange"
            :currentChange="handleCurrentChange"
          />
        </el-card>
      </div>
    </el-card>
  </div>
</template>

<script>
import Form from "@/components/Combinecom/form";
import echarts from "echarts/lib/echarts";
import EchartComp from "../../components/echartComp";
import Table from "@/components/Combinecom/table";
import { handleFormatMoney } from "@/utils/validate";
import API from "@/api/dataCenter";
import { downloadExcel } from "@/utils";
import dayjs from "dayjs";

export default {
  name: "detail",
  components: {
    EchartComp,
    Table,
    Form,
  },
  computed: {
    // 查询表单项
    searchFormItems() {
      return [
        {
          type: "datetimerange",
          size: "small",
          label: "选择时间",
          placeholder: "请选择日期",
          value: "rangeTime",
          valueFormat: "yyyy-MM-dd HH:mm:ss",
        },
      ];
    },
    // 查询区按钮配置项
    operationBtns() {
      return [
        {
          class: "is-plain",
          type: "primary",
          size: "small",
          label: "搜索",
          icon: "el-icon-search",
          click: () => this.handleSearch(),
        },
        {
          class: "is-plain",
          type: "info",
          size: "small",
          label: "重置",
          icon: "el-icon-refresh-left",
          click: () => this.handleReset(),
        },
      ];
    },
  },
  data() {
    return {
      // 查询条件栏
      searchForm: {
        startDate: "",
        endDate: "",
        rangeTime: [],
      },
      dataList: [
        {
          label: "今日营业额(元)",
          num: 0,
          value: "todayIncome",
          isLoading: true,
        },
        {
          label: "会员数(个)",
          num: 0,
          value: "memberCount",
          isLoading: true,
        },
        {
          label: "订单数(个)",
          num: 0,
          value: "orderCount",
          isLoading: true,
        },
      ],
      options0: {}, // 折线图配置  会员消费
      options1: {}, // 饼图配置    服务类型
      options2: {}, // 纵向柱状图  会员卡充值
      options3: {}, // 横向柱状图  药品销售
      chartData: {
        dates: [],
        store: [],
        health: [],
        medicine: [],
        all: [],
      },
      chartData2: {
        dates: [],
        amounts: [],
      },
      chartData3: {
        medicines: [],
        sales: [],
      },
      colors: ["#048DB8", "#10C8B8", "#FF6B81"],
      legendData: [],
      // select
      // selectedOption: '', // 默认选中 "全部消费"
      // options: [
      // 	{ value: '', label: '全部消费' },
      // 	{ value: 'tollType_5', label: '门诊诊疗' },
      // 	{ value: 'tollType_6', label: '药膳订单' },
      // 	{ value: 'tollType_7', label: '健康管理' },
      // ],
      // 表数据
      table: {
        // table参数
        total: 0,
        tableList: [],
        pagination: {
          limit: 20,
          offset: 0,
        },
        columns: [
          {
            label: "日期",
            prop: "consumeDate",
          },
          {
            label: "会员",
            prop: "memberName",
          },
          {
            label: "服务类型",
            prop: "serviceType",
          },
          {
            label: "金额(元)",
            prop: "consumeAmt",
            formatter: (row) => {
              return handleFormatMoney(row.consumeAmt);
            },
          },
          {
            label: "支付方式",
            prop: "payType",
          },
          {
            label: "操作员",
            prop: "doctorName",
          },
        ],
        isTableLoading: false, // 表格loading
        isExportLoading: false, // 导出loading
      },
    };
  },
  mounted() {
    this.getAllStatistics();
  },
  methods: {
    // 查询
    handleSearch() {
      this.getTableData();
    },
    // 重置
    handleReset() {
      this.searchForm.rangeTime = null;
      this.table.pagination.offset = 0;

      this.getTableData();
    },
    // total
    handleSizeChange(val) {
      this.table.pagination.offset = val;
      this.getTableData();
    },
    // 改变页数
    handleCurrentChange(val) {
      this.table.pagination.offset = val;
      this.getTableData();
    },
    // 返回列表页
    handleBack() {
      this.$router.push({
        path: "/singleDataStatistics",
      });
    },
    // 配置图表
    handleConfigEcharts() {
      this.options0 = {
        legend: {
          left: "center",
          data: ["门诊诊疗", "健康管理", "药膳订单"],
        },
        tooltip: {
          trigger: "axis",
          formatter: function (params) {
            // params 是当前横坐标下所有系列的数据
            let result = params[0].axisValue + "<br/>";
            let total = 0;

            params.forEach((item) => {
              result += `${item.seriesName}: ${item.value}个<br/>`;
              total += item.value;
            });

            result += `总消费数: ${total}个`;

            return result;
          },
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          axisLine: {
            onZero: true, // 确保轴线从原点开始
            lineStyle: {
              color: "#999",
            },
          },
          axisLabel: {
            color: "#B2B3B5",
            padding: [10, 0, 0, 0],
          },
          axisTick: {
            alignWithLabel: true, // 刻度线与标签对齐
          },
          boundaryGap: false, // 第一个和最后一个标签与坐标轴边界对齐
          data: this.chartData.dates,
        },
        yAxis: {
          type: "value",
          // min: 0,
          // max: 2500,
          // interval: 500,
          name: "(个)",
          nameTextStyle: {
            padding: [0, 60, 0, 10], // 调整位置
          },
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          splitLine: {
            lineStyle: {
              type: "solid",
              color: "#E8ECF4",
            },
          },
          axisLabel: {
            color: "#B2B3B5",
            padding: [0, 10, 0, 0],
          },
        },
        series: [
          {
            name: "门诊诊疗",
            type: "line",
            smooth: true,
            symbol: "emptyCircle",
            symbolSize: 6,
            itemStyle: {
              color: "#048DB8",
              borderWidth: 2,
              borderColor: "#048DB8",
            },
            lineStyle: {
              color: "#048DB8",
              width: 3,
            },
            data: this.chartData.store,
          },
          {
            name: "健康管理",
            type: "line",
            smooth: true,
            symbol: "emptyCircle",
            symbolSize: 6,
            itemStyle: {
              color: "#10C8B8",
              borderWidth: 2,
              borderColor: "#10C8B8",
            },
            lineStyle: {
              color: "#10C8B8",
              width: 3,
            },
            data: this.chartData.health,
          },
          {
            name: "药膳订单",
            type: "line",
            smooth: true,
            symbol: "emptyCircle",
            symbolSize: 6,
            itemStyle: {
              color: "#FF6B81",
              borderWidth: 2,
              borderColor: "#FF6B81",
            },
            lineStyle: {
              color: "#FF6B81",
              width: 3,
            },
            data: this.chartData.medicine,
          },
        ],
      };
      this.options1 = {
        legend: { left: "center" },
        tooltip: {
          trigger: "item",
          axisPointer: {
            type: "shadow",
          },
          formatter: "{b}<br/>服务类型数: {c}个",
        },
        graphic: [
          {
            // 控制环形中间出现字
            type: "text",
            left: "center",
            top: "48%",
            style: {
              fill: "#4A4A4A",
              text: "会员服务类型分布",
              fontSize: 16,
              textAlign: "center",
            },
          },
        ],
        series: [
          {
            name: "会员服务类型分布",
            type: "pie",
            radius: ["50%", "70%"],
            center: ["50%", "60%"],
            // roseType: 'radius',  // 设置为玫瑰图
            avoidLabelOverlap: false,
            padAngle: 10,
            itemStyle: {
              borderRadius: 10,
              borderColor: "#fff",
              borderWidth: 2,
            },
            label: {
              show: false,
              position: "center",
            },
            emphasis: {
              label: {
                formatter: `{c}`,
                show: true,
                fontSize: 24,
                fontWeight: "bold",
                color: "#4994E0",
                padding: [30, 0, 0, 0],
              },
            },
            labelLine: {
              show: false,
            },
            data: this.legendData.map((item, index) => ({
              value: item.value,
              name: item.name,
              itemStyle: {
                color: this.colors[index],
              },
            })),
          },
        ],
      };
      this.options2 = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
          formatter: function (params) {
            let result = params[0].name + "<br/>";
            params.forEach(function (item) {
              result += `${
                item.seriesName
              }: ${handleFormatMoney(item.value)}<br/>`;
            });
            return result;
          },
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          axisLine: {
            lineStyle: {
              color: "#999",
            },
          },
          axisTick: {
            alignWithLabel: false,
          },
          axisLabel: {
            color: "#B2B3B5",
            padding: [10, 0, 0, 0],
          },
          data: this.chartData2.dates,
        },
        yAxis: {
          type: "value",
          name: "(元)",
          nameTextStyle: {
            padding: [0, 60, 0, 10], // 调整位置
          },
          // min: 0,
          // max: 12000,
          // interval: 2000,
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: "#B2B3B5",
            padding: [0, 10, 0, 0],
          },
          splitLine: {
            lineStyle: {
              type: "solid",
              color: "#E8ECF4",
            },
          },
        },
        series: [
          {
            name: "充值金额",
            type: "bar",
            barWidth: "60%",
            data: this.chartData2.amounts,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: "#0090B6" },
                { offset: 0.5, color: "#00A8B5" },
                { offset: 1, color: "#27A59F" },
              ]),
            },
          },
        ],
      };
      this.options3 = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
          formatter: "{b}<br/>销售量: {c}个",
        },
        grid: {
          left: "3%",
          right: "10%",
          top: "3%",
          bottom: "3%",
          containLabel: true,
        },
        yAxis: {
          type: "category",
          data: this.chartData3.medicines,
          axisLine: {
            lineStyle: {
              color: "#999",
            },
          },
          axisTick: {
            alignWithLabel: false,
          },
          axisLabel: {
            color: "#B2B3B5",
            padding: [0, 10, 0, 0],
          },
        },
        xAxis: {
          type: "value",
          min: 0,
          max: 120,
          interval: 20,
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: "#B2B3B5",
            padding: [10, 0, 0, 0],
          },
          splitLine: {
            lineStyle: {
              type: "solid",
              color: "#E8ECF4",
            },
          },
        },
        series: [
          {
            name: "销售量",
            type: "bar",
            barWidth: "60%",
            data: this.chartData3.sales,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                { offset: 0, color: "#27A59F" },
                { offset: 0.5, color: "#00A8B5" },
                { offset: 1, color: "#0090B6" },
              ]),
            },
            label: {
              show: true,
              position: "right",
              formatter: "{c}",
              color: "#333",
            },
          },
        ],
      };
    },
    // 图表API
    async getAllStatistics() {
      Promise.all([
        this.getSingleTotal(),
        this.getMemberConsume(),
        this.getServiceType(),
        this.getMemberRecharge(),
        this.getSaleTop(),
        this.getTableData(),
      ])
        .then(() => {
          this.handleConfigEcharts();
        })
        .catch((err) => {
          this.$resetMessage.warning(err);
        });
    },

    // 今日营业额，会员总数，订单数
    async getSingleTotal() {
      try {
        const res = await API.reqSingleStatisticsDetail(this.$route.query.id);
        // console.log('id', res)
        if (res.code === "100") {
          this.dataList.map((v) => {
            for (let i in res.data) {
              if (v.value === i) {
                if (i === "todayIncome") {
                  v.num = handleFormatMoney(res.data[i]);
                } else {
                  v.num = res.data[i];
                }
              }
            }
          });
        } else {
          this.$message.warning(res.msg);
        }
      } catch (error) {
        this.$message.error(error);
      }
    },

    // 服务类型分布
    async getServiceType() {
      try {
        const res = await API.reqSingleServieType(this.$route.query.id);
        // console.log('id', res)
        if (res.code === "100") {
          for (let v in res.data) {
            // console.log('v', v)
            this.legendData.reverse().push({
              name: v,
              value: res.data[v] ? res.data[v] : 0,
            });
          }
        } else {
          this.$message.warning(res.msg);
        }
      } catch (error) {
        this.$message.error(error);
      }
    },

    // 会员消费趋势
    async getMemberConsume(tollType = "") {
      this.legendData2 = [];
      // console.log('tollType', tollType)
      try {
        const res = await API.reqSingleStatisticsMemberConsume(
          this.$route.query.id,
          tollType
        );
        // console.log('会员消费趋势', res)
        if (res.code === "100") {
          res.data.map((v) => {
            this.chartData.dates.push(dayjs(v.date).format("M-D"));
            if (v.list.length) {
              v.list.map((vv) => {
                if (vv.tollType === "tollType_5") {
                  this.chartData.store.push(vv.memberCount);
                }
                if (vv.tollType === "tollType_6") {
                  this.chartData.medicine.push(vv.memberCount);
                }
                if (vv.tollType === "tollType_7") {
                  this.chartData.health.push(vv.memberCount);
                }
              });
            } else {
              this.chartData.store.push(0);
              this.chartData.health.push(0);
              this.chartData.medicine.push(0);
            }
          });
        } else {
          this.$message.warning(res.msg);
        }
      } catch (error) {
        this.$message.error(error);
      }
    },

    // 会员充值记录
    async getMemberRecharge() {
      try {
        const res = await API.reqSingleRecharge(this.$route.query.id);
        // console.log('id', res)
        if (res.code === "100") {
          this.chartData2.dates = res.data.map((v) =>
            dayjs(v.date).format("M-D")
          );
          this.chartData2.amounts = res.data.map((v) =>
            v.count ? v.count : 0
          );
        } else {
          this.$message.warning(res.msg);
        }
      } catch (error) {
        this.$message.error(error);
      }
    },

    // 药瓶销售TOP10
    async getSaleTop() {
      try {
        const res = await API.reqSingleSale(this.$route.query.id);
        // console.log('id', res)
        if (res.code === "100") {
          this.chartData3.medicines = res.data.map((v) => v.name);
          this.chartData3.sales = res.data.map((v) => (v.count ? v.count : 0));
        } else {
          this.$message.warning(res.msg);
        }
      } catch (error) {
        this.$message.error(error);
      }
    },

    // 导出
    async handleExport() {
      const data = {
        companyId: this.$route.query.id,
        startDate: this.searchForm.rangeTime
          ? this.searchForm.rangeTime[0]
          : "",
        endDate: this.searchForm.rangeTime ? this.searchForm.rangeTime[1] : "",
        limit: this.table.pagination.limit,
        offset: this.table.pagination.offset,
      };
      this.isExportLoading = true;

      try {
        const res = await API.reqSingleExport(data);
        downloadExcel(res, "分店消费明细.xlsx");

        // console.log('res', res)
        this.$message.success("导出成功!");
        this.isExportLoading = false;
      } catch (error) {
        this.isExportLoading = false;
        this.$message.error(error);
      }
    },

    // table数据
    async getTableData() {
      const data = {
        companyId: this.$route.query.id,
        startDate: this.searchForm.rangeTime
          ? this.searchForm.rangeTime[0]
          : "",
        endDate: this.searchForm.rangeTime ? this.searchForm.rangeTime[1] : "",
        limit: this.table.pagination.limit,
        offset: this.table.pagination.offset ? (this.table.pagination.offset - 1) * this.table.pagination.limit : 0,
      };
      // console.log('data', data)
      // return
      try {
        this.table.isTableLoading = true;
        const res = await API.reqSingleConsume(data);
        if (res.code === "100") {
          this.table.tableList = res.data.rows || [];
          this.table.total = Number(res.data.total);
        } else {
          this.$message.warning(res.msg);
        }
        this.table.isTableLoading = false;
      } catch (error) {
        this.table.isTableLoading = false;
        // this.$message.warning(error);
        this.$message.error(error);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.HomeBackBtn {
  position: relative;
  top: -20px;
}

.homeHeader {
  display: flex;
  font-size: 16px;

  .homeTitleAndNum {
    width: 40%;
    margin-right: 15px;

    .title {
      font-size: 16px;
      color: #6c6c6c;
    }

    .num {
      font-size: 28px;
      color: #000000;
    }
  }
}

.homeHeader .homeTitleAndNum:last-child {
  margin-right: 0;
}

.homeContent,
.homeContent2 {
  box-sizing: border-box;
  display: flex;
  margin-top: 20px;

  .item {
    width: 50%;
    margin-right: 10px;
    height: 320px;

    .text {
      font-size: 16px;
      color: #333;
      margin: 0;
    }

    .homeEcharts0,
    .homeEcharts1,
    .homeEcharts2,
    .homeEcharts3,
    /deep/ .el-card__body {
      height: 270px;
    }

    /deep/ .custom-tooltip-style {
      padding: 0 16px;
      width: auto;
      height: 48px;
      line-height: 48px;
      background-color: #ffffff;
      text-align: center;
      box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.0999);
      border-radius: 2px 2px 2px 2px;
    }
  }
}

.homeContent .item:last-child {
  margin-right: 0;
}

.homeContent2 .item:last-child {
  margin-right: 0;
}

.homeContent2-5 {
  padding-top: 15px;
  box-sizing: border-box;
  display: flex;
  margin-top: 20px;
  width: 100%;

  .demo-form-inline {
    padding-left: 25px;
  }
}

.homeContent3 {
  box-sizing: border-box;
  display: flex;
  margin-top: 20px;
  width: 100%;

  .item {
    width: 100%;

    .text {
      display: inline-block;
      font-size: 16px;
      color: #333;
      margin: 0;
    }

    .export {
      float: right;
    }
  }

  .tableStyle {
    margin-top: 20px;
  }
}

.itemStyle {
  position: relative;

  .selectStyle {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 103px;
    height: 20px;
    line-height: 20px;
  }
}
</style>
