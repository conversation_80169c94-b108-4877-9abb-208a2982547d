export default {
  fu: {
    search_bar: {
      search: '搜索',
      adv_search: '高級搜索',
      ok: '确定',
      cancel: '取消',
      please_select: '请选择',
      please_input: '请输入',
      like: '包含',
      not_like: '不包含',
      in: '属于',
      not_in: '不屬于',
      gt: '大于',
      ge: '大于等于',
      lt: '小于',
      le: '小于等于',
      eq: '等于',
      ne: '不等于',
      between: '之间',
      select_date: '选择日期',
      start_date: '开始日期',
      end_date: '結束日期',
      select_date_time: '选择日期时间',
      start_date_time: '开始日期时间',
      end_date_time: '結束日期时间',
      range_separator: '至',
      data_time_error: '开始日期不能大于結束日期',
      clean: '清空',
      refresh: '刷新'
    },
    table: {
      ok: '确定',
      custom_table_fields: '自定义表格字段',
      custom_table_fields_desc: '固定字段不在选择范围'
    },
    steps: {
      cancel: '取消',
      next: '下一步',
      prev: '上一步',
      finish: '完成'
    }
  },
  route: {
    dashboard: '首页',
    documentation: '文档',
    guide: '引导页',
    permission: '权限测试页',
    rolePermission: '角色权限',
    pagePermission: '页面权限',
    directivePermission: '指令权限',
    icons: '图标',
    components: '组件',
    tinymce: '富文本编辑器',
    markdown: 'Markdown',
    jsonEditor: 'JSON 编辑器',
    dndList: '列表拖拽',
    splitPane: 'Splitpane',
    avatarUpload: '头像上传',
    dropzone: 'Dropzone',
    sticky: 'Sticky',
    countTo: 'Count To',
    componentMixin: '小组件',
    backToTop: '返回顶部',
    dragDialog: '拖拽 Dialog',
    dragSelect: '拖拽 Select',
    dragKanban: '可拖拽看板',
    charts: '图表',
    keyboardChart: '键盘图表',
    lineChart: '折线图',
    mixChart: '混合图表',
    example: '综合实例',
    nested: '路由嵌套',
    menu1: '菜单1',
    'menu1-1': '菜单 1-1',
    'menu1-2': '菜单 1-2',
    'menu1-2-1': '菜单 1-2-1',
    'menu1-2-2': '菜单 1-2-2',
    'menu1-3': '菜单 1-3',
    menu2: '菜单 2',
    Table: 'Table',
    dynamicTable: '动态 Table',
    dragTable: '拖拽 Table',
    inlineEditTable: 'Table 内编辑',
    complexTable: '综合 Table',
    tab: 'Tab',
    form: '表单',
    createArticle: '创建文章',
    editArticle: '编辑文章',
    articleList: '文章列表',
    errorPages: '错误页面',
    page401: '401',
    page404: '404',
    errorLog: '错误日志',
    excel: 'Excel',
    exportExcel: '导出 Excel',
    selectExcel: '导出 已选择项',
    mergeHeader: '导出 多级表头',
    uploadExcel: '上传 Excel',
    zip: 'Zip',
    pdf: 'PDF',
    exportZip: 'Export Zip',
    theme: '换肤',
    clipboardDemo: 'Clipboard',
    i18n: '国际化',
    externalLink: '外链',
    profile: '个人中心'
  },
  navbar: {
    dashboard: '首页',
    github: '项目地址',
    logOut: '退出登录',
    profile: '个人中心',
    theme: '换肤',
    size: '布局大小'
  },
  login: {
    title: '系统登录',
    welcome: '欢迎使用',
    logIn: '登录',
    username: '账号',
    password: '密码',
    any: '随便填',
    thirdparty: '第三方登录',
    thirdpartyTips: '本地不能模拟，请结合自己业务进行模拟！！！',
    expires: '登录信息过期，请重新登录',
    tokenError: '登陆信息错误，请重新登录',
    username_error: '请输入正确的 ID',
    password_error: '密码不小于 8 位',
    re_login: '重新登录',
    default_login: '普通登录'
  },
  commons: {
    search: '搜索',
    folder: '目录',
    no_target_permission: '没有权限',
    success: '成功',
    switch_lang: '切换语言成功',
    close: '关闭',
    icon: '图标',
    all: '全部',
    enable: '启用',
    disable: '禁用',
    yes: '是',
    no: '否',
    reset: '重置',
    catalogue: '目录',
    button: '按钮',
    gender: '性别',
    man: '男',
    woman: '女',
    nick_name: '姓名',
    confirmPassword: '确认密码',
    upload: '上传',
    cover: '覆盖',
    not_cover: '不覆盖',
    import_mode: '导入模式',
    import_module: '导入模块',
    please_fill_in_the_template: '请填写模版内容',
    cut_back_old_version: '切回旧版',
    cut_back_new_version: '切回新版',
    comment: '评论',
    examples: '示例',
    help_documentation: '帮助文档',
    api_help_documentation: 'API文档',
    delete_cancelled: '已取消删除',
    workspace: '工作空间',
    organization: '组织',
    menu: '菜单',
    setting: '设置',
    project: '项目',
    about_us: '关于',
    current_project: '当前项目',
    name: '名称',
    description: '描述',
    annotation: '注释',
    clear: '清空',
    save: '保存',
    otherSave: '另存为',
    update: '更新',
    save_success: '保存成功',
    delete_success: '删除成功',
    copy_success: '复制成功',
    modify_success: '修改成功',
    delete_cancel: '已取消删除',
    confirm: '确定',
    cancel: '取消',
    prompt: '提示',
    operating: '操作',
    input_limit: '长度在 {0} 到 {1} 个字符',
    login: '登录',
    welcome: '一站式开源数据分析平台',
    username: '姓名',
    password: '密码',
    input_username: '请输入用户姓名',
    input_password: '请输入密码',
    test: '测试',
    create_time: '创建时间',
    update_time: '更新时间',
    add: '添加',
    member: '成员',
    email: '邮箱',
    phone: '电话',
    role: '角色',
    personal_info: '个人信息',
    api_keys: 'API Keys',
    quota: '配额管理',
    status: '状态',
    show_all: '显示全部',
    show: '显示',
    report: '报告',
    user: '用户',
    system: '系统',
    personal_setting: '个人设置',
    test_resource_pool: '测试资源池',
    system_setting: '系统设置',
    input_content: '请输入内容',
    create: '新建',
    edit: '编辑',
    copy: '复制',
    refresh: '刷新',
    remark: '备注',
    delete: '删除',
    reduction: '恢复',
    not_filled: '未填写',
    please_select: '请选择',
    search_by_name: '根据名称搜索',
    personal_information: '个人信息',
    exit_system: '退出系统',
    verification: '验证',
    title: '标题',
    custom: '自定义',
    select_date: '选择日期',
    months_1: '一月',
    months_2: '二月',
    months_3: '三月',
    months_4: '四月',
    months_5: '五月',
    months_6: '六月',
    months_7: '七月',
    months_8: '八月',
    months_9: '九月',
    months_10: '十月',
    months_11: '十一月',
    months_12: '十二月',
    weeks_0: '周日',
    weeks_1: '周一',
    weeks_2: '周二',
    weeks_3: '周三',
    weeks_4: '周四',
    weeks_5: '周五',
    weeks_6: '周六',
    system_parameter_setting: '系统参数设置',
    connection_successful: '连接成功',
    connection_failed: '连接失败',
    save_failed: '保存失败',
    host_cannot_be_empty: '主机不能为空',
    port_cannot_be_empty: '端口号不能为空',
    account_cannot_be_empty: '帐户不能为空',
    remove: '移除',
    remove_cancel: '移除取消',
    remove_success: '移除成功',
    tips: '认证信息已过期，请重新登录',
    not_performed_yet: '尚未执行',
    incorrect_input: '输入内容不正确',
    delete_confirm: '请输入以下内容，确认删除：',
    login_username: 'ID 或 邮箱',
    input_login_username: '请输入用户 ID 或 邮箱',
    input_name: '请输入名称',
    please_upload: '请上传文件',
    please_fill_path: '请填写ur路径',
    formatErr: '格式错误',
    please_save: '请先保存',
    reference_documentation: '参考文档',
    id: 'ID',
    millisecond: '毫秒',
    cannot_be_null: '不能为空',
    required: '{0}是必填的',
    already_exists: '名称不能重复',
    modifier: '修改人',
    validate: '校验',
    batch_add: '批量添加',
    tag_tip: '输入回车添加标签',
    table: {
      select_tip: '已选中 {0} 条数据'
    },
    date: {
      select_date: '选择日期',
      start_date: '开始日期',
      end_date: '结束日期',
      select_date_time: '选择日期时间',
      start_date_time: '开始日期时间',
      end_date_time: '结束日期时间',
      range_separator: '至',
      data_time_error: '开始日期不能大于结束日期'
    },
    adv_search: {
      title: '高级搜索',
      combine: '组合查询',
      test: '所属测试',
      project: '所属项目',
      search: '查询',
      reset: '重置',
      and: '所有',
      or: '任意一个',
      operators: {
        is_empty: '空',
        is_not_empty: '非空',
        like: '包含',
        not_like: '不包含',
        in: '属于',
        not_in: '不属于',
        gt: '大于',
        ge: '大于等于',
        lt: '小于',
        le: '小于等于',
        equals: '等于',
        not_equals: '不等于',
        between: '之间',
        current_user: '是当前用户'
      },
      message_box: {
        alert: '警告',
        confirm: '确认'
      }
    },
    monitor: '监控',
    image: '镜像',
    tag: '标签',
    module: {
      select_module: '选择模块',
      default_module: '默认模块'
    },
    datasource: '数据源',
    char_can_not_more_50: '不能超过50字符',
    share_success: '分享成功',
    input_id: '请输入ID',
    input_pwd: '请输入密码',
    message_box: {
      alert: '警告',
      confirm: '确认',
      ok: '确认',
      cancel: '取消'
    },
    ukey_title: 'API Keys',
    thumbnail: '缩略图'
  },
  documentation: {
    documentation: '文档',
    github: 'Github 地址'
  },
  permission: {
    addRole: '新增角色',
    editPermission: '编辑权限',
    roles: '你的权限',
    switchRoles: '切换权限',
    tips: '在某些情况下，不适合使用 v-permission。例如：Element-UI 的 el-tab 或 el-table-column 以及其它动态渲染 dom 的场景。你只能通过手动设置 v-if 来实现。',
    delete: '删除',
    confirm: '确定',
    cancel: '取消'
  },
  guide: {
    description: '引导页对于一些第一次进入项目的人很有用，你可以简单介绍下项目的功能。本 Demo 是基于',
    button: '打开引导'
  },
  components: {
    documentation: '文档',
    tinymceTips: '富文本是管理后台一个核心的功能，但同时又是一个有很多坑的地方。在选择富文本的过程中我也走了不少的弯路，市面上常见的富文本都基本用过了，最终权衡了一下选择了Tinymce。更详细的富文本比较和介绍见',
    dropzoneTips: '由于我司业务有特殊需求，而且要传七牛 所以没用第三方，选择了自己封装。代码非常的简单，具体代码你可以在这里看到 @/components/Dropzone',
    stickyTips: '当页面滚动到预设的位置会吸附在顶部',
    backToTopTips1: '页面滚动到指定位置会在右下角出现返回顶部按钮',
    backToTopTips2: '可自定义按钮的样式、show/hide、出现的高度、返回的位置 如需文字提示，可在外部使用Element的el-tooltip元素',
    imageUploadTips: '由于我在使用时它只有vue@1版本，而且和mockjs不兼容，所以自己改造了一下，如果大家要使用的话，优先还是使用官方版本。'
  },
  table: {
    dynamicTips1: '固定表头, 按照表头顺序排序',
    dynamicTips2: '不固定表头, 按照点击顺序排序',
    dragTips1: '默认顺序',
    dragTips2: '拖拽后顺序',
    title: '标题',
    importance: '重要性',
    type: '类型',
    remark: '点评',
    search: '搜索',
    add: '添加',
    export: '导出',
    reviewer: '审核人',
    id: '序号',
    date: '时间',
    author: '作者',
    readings: '阅读数',
    status: '状态',
    actions: '操作',
    edit: '编辑',
    publish: '发布',
    draft: '草稿',
    delete: '删除',
    cancel: '取 消',
    confirm: '确 定'
  },
  detabs: {
    eidttitle: '编辑标题',
    selectview: '选择视图'
  },
  example: {
    warning: '创建和编辑页面是不能被 keep-alive 缓存的，因为keep-alive 的 include 目前不支持根据路由来缓存，所以目前都是基于 component name 来进行缓存的。如果你想类似的实现缓存效果，可以使用 localStorage 等浏览器缓存方案。或者不要使用 keep-alive 的 include，直接缓存所有页面。详情见'
  },
  errorLog: {
    tips: '请点击右上角bug小图标',
    description: '现在的管理后台基本都是spa的形式了，它增强了用户体验，但同时也会增加页面出问题的可能性，可能一个小小的疏忽就导致整个页面的死锁。好在 Vue 官网提供了一个方法来捕获处理异常，你可以在其中进行错误处理或者异常上报。',
    documentation: '文档介绍'
  },
  excel: {
    export: '导出',
    selectedExport: '导出已选择项',
    placeholder: '请输入文件名(默认excel-list)'
  },
  zip: {
    export: '导出',
    placeholder: '请输入文件名(默认file)'
  },
  pdf: {
    tips: '这里使用   window.print() 来实现下载pdf的功能'
  },
  theme: {
    change: '换肤',
    documentation: '换肤文档',
    tips: 'Tips: 它区别于 navbar 上的 theme-pick, 是两种不同的换肤方法，各自有不同的应用场景，具体请参考文档。',
    base: '基础配色',
    font: '字体颜色',
    border: '边框颜色',
    background: '背景颜色',
    custom: '自定义颜色',
    otherSave: '主题另存为',
    info: '主题信息'
  },
  tagsView: {
    refresh: '刷新',
    close: '关闭',
    closeOthers: '关闭其它',
    closeAll: '关闭所有'
  },
  settings: {
    title: '系统布局配置',
    theme: '主题',
    tagsView: '开启 Tags-View',
    fixedHeader: '固定 Header',
    sidebarLogo: '侧边栏 Logo'
  },
  sysParams: {
    display: '显示设置',
    ldap: 'LDAP设置',
    oidc: 'OIDC设置',
    theme: '主题设置'
  },
  license: {
    i18n_no_license_record: '没有 License 记录',
    i18n_license_is_empty: 'License 为空',
    title: '授权管理',
    corporation: '客户名称',
    time: '授权时间',
    product: '产品名称',
    edition: '产品版本',
    licenseVersion: '授权版本',
    count: '授权数量',
    valid_license: '授权验证',
    show_license: '查看授权',
    valid_license_error: '授权验证失败',
    status: '授权状态',
    valid: '有效',
    invalid: '无效',
    expired: '已过期'
  },
  member: {
    create: '添加成员',
    modify: '修改成员',
    delete_confirm: '这个用户确定要删除吗?',
    please_choose_member: '请选择成员',
    search_by_name: '根据名称搜索',
    modify_personal_info: '修改个人信息',
    edit_password: '修改密码',
    edit_information: '编辑信息',
    input_name: '请输入名称',
    input_email: '请输入邮箱',
    special_characters_are_not_supported: '不支持特殊字符',
    mobile_number_format_is_incorrect: '手机号码格式不正确',
    email_format_is_incorrect: '邮箱格式不正确',
    password_format_is_incorrect: '有效密码：8-30位，英文大小写字母+数字+特殊字符（可选）',
    old_password: '旧密码',
    new_password: '新密码',
    repeat_password: '确认密码',
    inconsistent_passwords: '两次输入的密码不一致',
    remove_member: '确定要移除该成员吗',
    org_remove_member: '将该用户从组织中移除，将同时移除该组织下所有工作空间的权限，确定要移除该成员吗？',
    input_id_or_email: '请输入用户 ID, 或者 用户邮箱',
    no_such_user: '无此用户信息, 请输入正确的用户 ID 或者 用户邮箱！'
  },
  user: {
    create: '新建用户',
    modify: '修改用户',
    input_name: '请输入用户姓名',
    input_id: '请输入ID',
    input_email: '请输入邮箱',
    input_password: '请输入密码',
    input_phone: '请输入电话号码',
    input_roles: '请选择角色',
    select_users: '请选择用户',
    special_characters_are_not_supported: '不支持特殊字符',
    mobile_number_format_is_incorrect: '手机号码格式不正确',
    email_format_is_incorrect: '邮箱格式不正确',
    delete_confirm: '这个用户确定要删除吗?',
    apikey_delete_confirm: '这个 API Key 确定要删除吗?',
    input_id_placeholder: '请输入ID (不支持中文)',
    source: '用户来源',
    choose_org: '选择组织',
    reset_password: '重置密码',
    current_user: '当前用户',
    origin_passwd: '原始密码',
    new_passwd: '新密码',
    confirm_passwd: '确认密码',
    change_password: '修改密码',
    search_by_name: '按姓名搜索',
    import_ldap: '导入LDAP用户'
  },
  ldap: {
    url: 'LDAP地址',
    dn: '绑定DN',
    password: '密码',
    ou: '用户OU',
    filter: '用户过滤器',
    mapping: 'LDAP属性映射',
    open: '启用LDAP认证',
    input_url: '请输入LDAP地址',
    input_dn: '请输入DN',
    input_password: '请输入密码',
    input_ou: '请输入用户OU',
    input_filter: '请输入用户过滤器',
    input_mapping: '请输入LDAP属性映射',
    input_username: '请输入用户名',
    input_url_placeholder: '请输入LDAP地址 (如 ldap://localhost:389)',
    input_ou_placeholder: '输入用户OU (使用|分隔各OU)',
    input_filter_placeholder: '输入过滤器 [可能的选项是cn或uid或sAMAccountName={0}, 如：(uid={0})]',
    input_mapping_placeholder: '如：{"userName":"uid","nickName":"cn","email":"mail"}, username映射的选项可能是cn或uid或sAMAccountName',
    test_connect: '测试连接',
    test_login: '测试登录',
    edit: '编辑',
    login_success: '登录成功',
    url_cannot_be_empty: 'LDAP 地址不能为空',
    dn_cannot_be_empty: 'LDAP DN不能为空',
    ou_cannot_be_empty: 'LDAP OU不能为空',
    filter_cannot_be_empty: 'LDAP 用户过滤器不能为空',
    mapping_cannot_be_empty: 'LDAP 用户属性映射不能为空',
    password_cannot_be_empty: 'LDAP 密码不能为空'
  },
  oidc: {
    auth_endpoint: '请输入AuthEndpoint',
    token_endpoint: '请输入TokenEndpoint',
    userinfo_endpoint: '请输入UserinfoEndpoint',
    logout_endpoint: '请输入logoutEndpoint',
    clientId: '请输入ClientId',
    secret: '请输入Secret',
    scope: '请输入scope',
    redirectUrl: '请输入redirectUrl',
    open: '启用OIDC认证'
  },
  role: {
    menu_authorization: '菜单授权',
    data_authorization: '数据授权',
    please_choose_role: '请选择角色',
    admin: '系统管理员',
    org_admin: '组织管理员',
    org_member: '组织成员',
    add: '新建角色',
    delete: '删除角色',
    modify: '修改角色',
    tips: '提示',
    confirm_delete: '确认删除角色 ',
    role_name: '角色名称',
    search_by_name: '按名称搜索',
    pls_input_name: '请输入名称'
  },
  menu: {
    parent_category: '上级目录',
    module_name: '组件名称',
    module_path: '组件路径',
    route_addr: '路由地址',
    menu_sort: '菜单排序',
    authority_identification: '权限标识',
    button_name: '按钮名称',
    select_icon: '选择图标',
    create_time: '创建日期',
    tile: '菜单标题',
    create: '创建菜单',
    modify: '修改菜单',
    delete: '删除菜单',
    delete_confirm: '确定删除菜单吗',
    menu_type: '菜单类型'
  },
  organization: {
    parent_org: '上级组织',
    select_parent_org: '选择上级组织',
    top_org: '顶级组织',
    name: '组织名称',
    sort: '组织排序',
    sub_organizations: '下属组织数',
    create_time: '创建日期',
    create: '新建组织',
    modify: '修改组织',
    delete: '删除组织',
    delete_confirm: '确定要删除该组织吗?',
    input_name: '请输入组织名称',
    select_organization: '请选择组织',
    search_by_name: '根据名称搜索',
    special_characters_are_not_supported: '格式错误(不支持特殊字符，且不能以\'-\'开头结尾)',
    select: '选择组织'
  },
  system_parameter_setting: {
    mailbox_service_settings: '邮件设置',
    test_connection: '测试连接',
    SMTP_host: 'SMTP主机',
    basic_setting: '基础设置',
    front_time_out: '请求超时时间(单位:秒, 注意:保存后刷新浏览器生效)',
    msg_time_out: '消息保留时间(单位:天)',
    empty_front: '为空则默认取10秒',
    empty_msg: '为空则默认取30天',
    front_error: '请填写0-100正整数',
    msg_error: '请填写1-365正整数',
    SMTP_port: 'SMTP端口',
    SMTP_account: 'SMTP账户',
    SMTP_password: 'SMTP密码',
    SSL: '开启SSL(如果SMTP端口是465，通常需要启用SSL)',
    TLS: '开启TLS(如果SMTP端口是587，通常需要启用TLS)',
    SMTP: '是否免密 SMTP',
    host: '主机号不能为空',
    port: '端口号不能为空',
    account: '账户不能为空',
    test_recipients: '测试收件人',
    tip: '提示：仅用来作为测试邮件收件人'
  },
  chart: {
    save_snapshot: '保存缩略图',
    datalist: '视图',
    add_group: '添加分组',
    add_scene: '添加场景',
    group: '分组',
    scene: '场景',
    delete: '删除',
    move_to: '移动到',
    rename: '重命名',
    tips: '提示',
    confirm_delete: '确认删除',
    delete_success: '删除成功',
    confirm: '确认',
    cancel: '取消',
    search: '搜索',
    back: '返回',
    add_table: '添加数据集',
    process: '进度',
    add_chart: '添加视图',
    db_data: '数据库数据集',
    sql_data: 'SQL数据集',
    excel_data: 'Excel数据集',
    custom_data: '自定义数据集',
    pls_slc_tbl_left: '请从左侧选视图',
    add_db_table: '添加数据库数据集',
    pls_slc_data_source: '请选择数据源',
    table: '表',
    edit: '编辑',
    create_view: '创建试图',
    data_preview: '数据预览',
    dimension: '维度',
    quota: '指标',
    title: '标题',
    show: '显示',
    chart_type: '图表类型',
    shape_attr: '图形属性',
    module_style: '组件样式',
    result_filter: '过滤器',
    x_axis: '横轴',
    y_axis: '纵轴',
    chart: '视图',
    close: '关闭',
    summary: '汇总方式',
    fast_calc: '快速计算',
    sum: '求和',
    count: '计数',
    avg: '平均',
    max: '最大值',
    min: '最小值',
    stddev_pop: '标准差',
    var_pop: '方差',
    quick_calc: '快速计算',
    show_name_set: '显示名设置',
    color: '颜色',
    color_case: '配色方案',
    pls_slc_color_case: '请选择配色方案',
    color_default: '默认',
    color_retro: '复古',
    color_future: '未来',
    color_gradual: '渐变',
    color_business: '商务',
    color_gentle: '柔和',
    color_elegant: '淡雅',
    color_technology: '科技',
    color_simple: '简洁',
    not_alpha: '不透明度',
    size: '大小',
    bar_width: '柱宽',
    bar_gap: '柱间隔',
    adapt: '自适应',
    line_width: '线宽',
    line_type: '线型',
    line_symbol: '折点',
    line_symbol_size: '折点大小',
    line_type_solid: '实线',
    line_type_dashed: '虚线',
    line_symbol_circle: '圆形',
    line_symbol_emptyCircle: '空心圆',
    line_symbol_rect: '矩形',
    line_symbol_roundRect: '圆角矩形',
    line_symbol_triangle: '三角形',
    line_symbol_diamond: '菱形',
    line_symbol_pin: '钉子',
    line_symbol_arrow: '箭头',
    line_symbol_none: '无',
    line_area: '面积',
    pie_inner_radius: '内径',
    pie_outer_radius: '外径',
    funnel_width: '宽度',
    line_smooth: '平滑折线',
    title_style: '标题样式',
    text_fontsize: '字体大小',
    text_color: '字体颜色',
    text_h_position: '水平位置',
    text_v_position: '垂直位置',
    text_pos_left: '左',
    text_pos_center: '中',
    text_pos_right: '右',
    text_pos_top: '上',
    text_pos_bottom: '下',
    text_italic: '字体倾斜',
    italic: '倾斜',
    orient: '方向',
    horizontal: '水平',
    vertical: '垂直',
    legend: '图例',
    shape: '形状',
    polygon: '多边形',
    circle: '圆形',
    label: '标签',
    label_position: '标签位置',
    content_formatter: '内容格式',
    inside: '内',
    tooltip: '提示',
    tooltip_item: '数据项',
    tooltip_axis: '坐标轴',
    formatter_plc: '内容格式为空时，显示默认格式',
    xAxis: '横轴',
    yAxis: '纵轴',
    position: '位置',
    rotate: '角度',
    name: '名称',
    icon: '图标',
    trigger_position: '触发位置',
    asc: '升序',
    desc: '降序',
    sort: '排序',
    filter: '过滤',
    none: '无',
    background: '背景',
    border: '边角',
    border_width: '边框宽度',
    border_radius: '边框半径',
    alpha: '透明度',
    add_filter: '添加过滤',
    no_limit: '无限制',
    filter_eq: '等于',
    filter_not_eq: '不等于',
    filter_lt: '小于',
    filter_le: '小于等于',
    filter_gt: '大于',
    filter_ge: '大于等于',
    filter_null: '为空',
    filter_not_null: '不为空',
    filter_empty: '空字符串',
    filter_not_empty: '非空字符串',
    filter_include: '包含',
    filter_not_include: '不包含',
    rose_type: '玫瑰图模式',
    radius_mode: '半径',
    area_mode: '面积',
    rose_radius: '圆角',
    view_name: '视图标题',
    belong_group: '所属分组',
    select_group: '选择分组',
    name_can_not_empty: '名称不能为空',
    template_can_not_empty: '请选择仪表版',
    custom_count: '记录数',
    table_title_fontsize: '表头字体大小',
    table_item_fontsize: '表格字体大小',
    table_header_bg: '表头背景',
    table_item_bg: '表格背景',
    table_item_font_color: '字体颜色',
    stripe: '斑马纹',
    start_angle: '起始角度',
    end_angle: '结束角度',
    style_priority: '样式优先级',
    dashboard: '仪表板',
    dimension_color: '名称颜色',
    quota_color: '值颜色',
    dimension_font_size: '名称字体大小',
    quota_font_size: '值字体大小',
    space_split: '名称/值间隔',
    only_one_quota: '仅支持1个指标',
    only_one_result: '仅显示第1个计算结果',
    dimension_show: '名称显示',
    quota_show: '值显示',
    title_limit: '标题不能大于50个字符',
    filter_condition: '过滤条件',
    filter_field_can_null: '过滤字段必填',
    preview_100_data: '预览前100条记录',
    chart_table_normal: '汇总表',
    chart_table_info: '明细表',
    chart_card: '指标卡',
    chart_bar: '基础柱状图',
    chart_bar_stack: '堆叠柱状图',
    chart_bar_horizontal: '横向柱状图',
    chart_bar_stack_horizontal: '横向堆叠柱状图',
    chart_line: '基础折线图',
    chart_line_stack: '堆叠折线图',
    chart_pie: '饼图',
    chart_pie_rose: '南丁格尔玫瑰图',
    chart_funnel: '漏斗图',
    chart_radar: '雷达图',
    chart_gauge: '仪表盘',
    chart_map: '地图',
    dateStyle: '日期显示',
    datePattern: '日期格式',
    y: '年',
    y_M: '年月',
    y_M_d: '年月日',
    H_m_s: '时分秒',
    y_M_d_H_m: '年月日时分',
    y_M_d_H_m_s: '年月日时分秒',
    date_sub: 'yyyy-MM-dd',
    date_split: 'yyyy/MM/dd',
    chartName: '新建视图',
    chart_show_error: '无法正常显示',
    chart_error_tips: '如有疑问请联系管理员',
    title_cannot_empty: '标题不能为空',
    table_title_height: '表头行高',
    table_item_height: '表格行高',
    axis_show: '轴线显示',
    axis_color: '轴线颜色',
    axis_width: '轴线宽度',
    axis_type: '轴线类型',
    axis_type_solid: '实线',
    axis_type_dashed: '虚线',
    axis_type_dotted: '点',
    axis_label_show: '标签显示',
    axis_label_color: '标签颜色',
    axis_label_fontsize: '标签大小',
    text_style: '字体样式',
    bolder: '加粗',
    change_ds: '更换数据集',
    change_ds_tip: '提示：更换数据集将导致字段发生变化，需重新制作视图',
    axis_name_color: '名称颜色',
    axis_name_fontsize: '名称字体',
    pie_label_line_show: '引导线',
    outside: '外',
    center: '中心',
    split: '轴线',
    axis_line: '轴线',
    axis_label: '轴标签',
    label_fontsize: '标签大小',
    split_line: '分割线',
    split_color: '分割颜色',
    shadow: '阴影',
    condition: '过滤值',
    filter_value_can_null: '过滤值不能为空',
    filter_like: '包含',
    filter_not_like: '不包含',
    color_light: '明亮',
    color_classical: '经典',
    color_fresh: '清新',
    color_energy: '活力',
    color_red: '火红',
    color_fast: '轻快',
    color_spiritual: '灵动',
    chart_details: '视图明细',
    export_details: '导出明细',
    chart_data: '数据',
    chart_style: '样式',
    drag_block_type_axis: '类别轴',
    drag_block_value_axis: '值轴',
    drag_block_table_data_column: '数据列',
    drag_block_pie_angel: '扇区角度',
    drag_block_pie_label: '扇区标签',
    drag_block_gauge_angel: '指针角度',
    drag_block_label_value: '值',
    drag_block_funnel_width: '漏斗层宽',
    drag_block_funnel_split: '漏斗分层',
    drag_block_radar_length: '分支长度',
    drag_block_radar_label: '分支标签',
    map_range: '地图范围',
    select_map_range: '请选择地图范围',
    area: '地区',
    stack_item: '堆叠项',
    placeholder_field: '拖动字段至此处',
    axis_label_rotate: '标签角度',
    chart_scatter_bubble: '气泡图',
    chart_scatter: '散点图',
    bubble_size: '气泡大小',
    chart_treemap: '矩形树图',
    drill: '钻取',
    drag_block_treemap_label: '色块标签',
    drag_block_treemap_size: '色块大小',
    bubble_symbol: '图形',
    gap_width: '间隔',
    width: '宽度',
    height: '高度',
    system_case: '系统方案',
    custom_case: '自定义',
    last_layer: '当前已经是最后一级',
    radar_size: '大小',
    chart_mix: '组合图',
    axis_value: '轴值',
    axis_value_min: '最小值',
    axis_value_max: '最大值',
    axis_value_split: '间隔',
    axis_auto: '自动',
    table_info_switch: '明细表切换将清空维度',
    drag_block_value_axis_main: '主轴值',
    drag_block_value_axis_ext: '副轴值',
    yAxis_main: '主纵轴',
    yAxis_ext: '副纵轴',
    total: '共',
    items: '条数据',
    chart_liquid: '水波图',
    drag_block_progress: '进度指示',
    liquid_max: '目标值',
    liquid_outline_border: '边框粗细',
    liquid_outline_distance: '边框间隔',
    liquid_wave_length: '水波长度',
    liquid_wave_count: '水波数量',
    liquid_shape: '形状',
    liquid_shape_circle: '圆形',
    liquid_shape_diamond: '菱形',
    liquid_shape_triangle: '三角形',
    liquid_shape_pin: '气球',
    liquid_shape_rect: '矩形',
    dimension_or_quota: '维度或指标',
    axis_value_split_count: '刻度数',
    chart_waterfall: '瀑布图',
    pie_inner_radius_percent: '内径占比',
    pie_outer_radius_size: '外径大小',
    table_page_size: '分页',
    table_page_size_unit: '条/页',
    result_count: '结果展示',
    result_mode_all: '全部',
    chart_word_cloud: '词云',
    drag_block_word_cloud_label: '词标签',
    drag_block_word_cloud_size: '词大小',
    splitCount_less_100: '刻度数范围0-100',
    change_chart_type: '更改类型',
    chart_type_table: '表格',
    chart_type_quota: '指标',
    chart_type_trend: '趋势',
    chart_type_compare: '比较',
    chart_type_distribute: '分布',
    chart_type_relation: '关系',
    chart_type_space: '空间位置',
    preview: '上一步',
    next: '下一步',
    select_dataset: '选择数据集',
    select_chart_type: '选择图表类型',
    recover: '重置'
  },
  dataset: {
    sheet_warn: '有多个 Sheet 页，默认抽取第一个',
    datalist: '数据集',
    name: '数据集名称',
    add_group: '添加分组',
    add_scene: '添加场景',
    group: '分组',
    scene: '场景',
    delete: '删除',
    move_to: '移动到',
    rename: '重命名',
    tips: '提示',
    confirm_delete: '确认删除',
    delete_success: '删除成功',
    confirm: '确认',
    cancel: '取消',
    search: '搜索',
    back: '返回',
    add_table: '添加数据集',
    process: '进度',
    update: '更新',
    db_data: '数据库数据集',
    sql_data: 'SQL 数据集',
    excel_data: 'Excel 数据集',
    custom_data: '自定义数据集',
    pls_slc_tbl_left: '请从左侧选择表',
    add_db_table: '添加数据库数据集',
    pls_slc_data_source: '请选择数据源',
    table: '表',
    edit: '编辑',
    create_view: '创建视图',
    data_preview: '数据预览',
    field_type: '字段类型',
    field_name: '字段名',
    field_origin_name: '原始名',
    field_check: '选中',
    update_info: '更新信息',
    join_view: '数据关联',
    text: '文本',
    time: '时间',
    value: '数值',
    mode: '模式',
    direct_connect: '直连',
    sync_data: '定时同步',
    update_setting: '更新设置',
    sync_now: '立即更新',
    add_task: '添加任务',
    task_name: '任务名称',
    task_id: '任务ID',
    start_time: '开始时间',
    end_time: '结束时间',
    status: '状态',
    error: '失败',
    completed: '成功',
    underway: '执行中',
    task_update: '更新设置',
    update_type: '更新方式',
    all_scope: '全量更新',
    add_scope: '增量更新',
    select_data_time: '选择日期时间',
    execute_rate: '执行频率',
    execute_once: '立即执行',
    simple_cron: '简单重复',
    cron_config: '表达式设定',
    no_limit: '无限制',
    set_end_time: '设定结束时间',
    operate: '操作',
    save_success: '保存成功',
    close: '关闭',
    required: '必填',
    input_content: '请输入内容',
    add_sql_table: '添加 SQL 数据集',
    preview: '预览',
    pls_input_name: '请输入名称',
    connect_mode: '连接模式',
    incremental_update_type: '增量更新方式',
    incremental_add: '增量添加',
    incremental_delete: '增量删除',
    last_update_time: '上次更新时间',
    current_update_time: '当前更新时间',
    param: '参数',
    edit_sql: '编辑 SQL 数据集',
    showRow: '显示行',
    add_excel_table: '添加Excel数据集',
    add_custom_table: '添加自定义数据集',
    upload_file: '上传文件',
    detail: '详情',
    type: '类型',
    create_by: '创建者',
    create_time: '创建时间',
    preview_show: '显示',
    preview_item: '条数据',
    preview_total: '共',
    pls_input_less_5: '请输入5位以内的正整数',
    field_edit: '编辑字段',
    table_already_add_to: '该表已添加至',
    uploading: '上传中...',
    add_union: '新建关联',
    union_setting: '关联设置',
    pls_slc_union_field: '请选择关联字段',
    pls_slc_union_table: '请选择关联表',
    source_table: '关联表',
    source_field: '关联字段',
    target_table: '被关联表',
    target_field: '被关联字段',
    union_relation: '关联关系',
    pls_setting_union_success: '请正确设置关联关系',
    invalid_dataset: 'Kettle未运行，无效数据集',
    check_all: '全选',
    can_not_union_self: '被关联表不能与关联表相同',
    float: '小数',
    edit_custom_table: '编辑自定义数据集',
    edit_field: '编辑字段',
    preview_100_data: '显示前100行数据',
    invalid_table_check: '非直连数据集请先完成数据同步',
    parse_error: 'Excel解析失败，请检查格式、字段等信息。具体参考：https://dataease.io/docs/faq/dataset_faq/',
    origin_field_type: '原始类型',
    edit_excel_table: '编辑Excel数据集',
    edit_excel: '编辑Excel',
    excel_replace: '替换',
    excel_add: '追加',
    dataset_group: '数据集分组',
    m1: '将 ',
    m2: ' 移动到',
    char_can_not_more_50: '数据集名称不能超过50个字符',
    task_add_title: '添加任务',
    task_edit_title: '编辑任务',
    sync_latter: '稍后同步',
    task: {
      list: '任务列表',
      record: '执行记录',
      create: '新建任务',
      name: '任务名称',
      last_exec_time: '上次执行时间',
      next_exec_time: '下次执行时间',
      last_exec_status: '上次执行结果',
      task_status: '任务状态',
      dataset: '数据集',
      search_by_name: '根据名称搜索',
      underway: '等待执行',
      stopped: '执行结束',
      pending: '暂停',
      exec: '执行一次',
      confirm_exec: '手动触发执行？',
      change_success: '状态切换成功'
    },
    field_group_type: '分类',
    location: '地理位置',
    left_join: '左连接',
    right_join: '右连接',
    inner_join: '内连接',
    full_join: '全连接',
    can_not_union_diff_datasource: '被关联数据集必须与当前数据集的数据源一致',
    operator: '操作',
    d_q_trans: '维度/指标转换',
    add_calc_field: '新建计算字段',
    input_name: '请输入名称',
    field_exp: '字段表达式',
    data_type: '数据类型',
    click_ref_field: '点击引用字段',
    click_ref_function: '点击引用函数',
    field_manage: '字段管理',
    edit_calc_field: '编辑计算字段',
    calc_field: '计算字段',
    show_sql: '显示SQL',
    ple_select_excel: '请选择要导入的 Excel',
    merge: '合并',
    no_merge: '不合并',
    merge_msg: '数据表中存在字段一致的情况，是否合并到一个数据集中?',
    merge_title: '合并数据',
    field_name_less_50: '字段名不能超过50个字符',
    excel_info_1: '1、文件中不能存在合并单元格；',
    excel_info_2: '2、文件的第一行为标题行，不能为空，不能为日期型；',
    excel_info_3: '3、Excel文件大小请确保在500M以内。',
    sync_field: '同步字段',
    confirm_sync_field: '确认同步',
    confirm_sync_field_tips: '同步字段可能会导致已编辑字段发生变更，请确认',
    sync_success: '同步成功',
    sync_success_1: '同步成功，请对当前数据集重新执行数据同步操作'
  },
  datasource: {
    datasource: '数据源',
    please_select_left: '请从左侧选择数据源',
    show_info: '数据源信息',
    create: '新建数据源',
    type: '类型',
    please_choose_type: '请选择数据源类型',
    please_choose_data_type: '请选择计算模式',
    data_base: '数据库名称',
    user_name: '用户名',
    password: '密码',
    host: '主机名/IP地址',
    port: '端口',
    datasource_url: '地址',
    please_input_datasource_url: '请输入 Elasticsearch 地址，如: http://es_host:es_port',
    please_input_data_base: '请输入数据库名称',
    please_select_oracle_type: '选择连接类型',
    please_input_user_name: '请输入用户名',
    please_input_password: '请输入密码',
    please_input_host: '请输入主机',
    please_input_url: '请输入URL地址',
    please_input_port: '请输入端口',
    modify: '编辑数据源',
    validate_success: '校验成功',
    validate: '校验',
    search_by_name: '根据名称搜索',
    delete_warning: '确定要删除吗?',
    input_name: '请输入名称',
    input_limit_2_25: '2-25字符',
    input_limit_0_50: '0-50字符',
    oracle_connection_type: '服务名/SID',
    oracle_sid: 'SID',
    oracle_service_name: '服务名',
    get_schema: '获取 Schema',
    schema: '数据库 Schema',
    please_choose_schema: '请选择数据库 Schema',
    edit_datasource_msg: '修改数据源信息，可能会导致改数据源下的数据集不可用，确认修改？',
    in_valid: '无效数据源',
    initial_pool_size: '初始连接数',
    min_pool_size: '最小连接数',
    max_pool_size: '最大连接数',
    max_idle_time: '最大空闲(秒)',
    acquire_increment: '增长数',
    connect_timeout: '连接超时(秒)',
    please_input_initial_pool_size: '请输入初始连接数',
    please_input_min_pool_size: '请输入最小连接数',
    please_input_max_pool_size: '请输入最大连接数',
    please_input_max_idle_time: '请输入最大空闲(秒)',
    please_input_acquire_increment: '请输入增长数',
    please_input_connect_timeout: '请输入连接超时(秒)',
    no_less_then_0: '高级设置中的参数不能小于零',
    port_no_less_then_0: '端口不能小于零',
    priority: '高级设置',
    data_mode: '数据模式',
    direct: '直连模式',
    extract: '抽取模式',
    all_compute_mode: '直连、抽取模式',
    extra_params: '额外的JDBC连接字符串'
  },
  pblink: {
    key_pwd: '请输入密码打开链接',
    input_placeholder: '请输入4位数字密码',
    pwd_error: '密码错误',
    pwd_format_error: '请输入4位数字密码',
    sure_bt: '确定'
  },
  panel: {
    no_auth_role: '未分享角色',
    auth_role: '已分享角色',
    picture_limit: '只能插入图片',
    drag_here: '请将左侧字段拖至此处',
    copy_link_passwd: '复制链接及密码',
    copy_link: '复制链接',
    copy_short_link: '复制短链接',
    copy_short_link_passwd: '复制短链接及密码',
    passwd_protect: '密码保护',
    link: '链接',
    over_time: '有效期',
    link_expire: '链接已过期！',
    link_share: '链接分享',
    link_share_desc: '开启链接后，任何人可通过此链接访问仪表板。',
    share: '分享',
    remove_share_confirm: '确认取消当前仪表板所有分享？',
    share_in: '分享给我',
    share_out: '我分享的',
    who_share: '分享人',
    when_share: '分享时间',
    share_to: '分享对象',
    org: '组织',
    role: '角色',
    user: '用户',
    datalist: '视图列表',
    group: '目录',
    panel: '仪表板',
    panel_list: '仪表板',
    groupAdd: '新建目录',
    panelAdd: '新建仪表板',
    delete: '删除',
    move_to: '移动到',
    rename: '重命名',
    import: '导入模板',
    tips: '提示',
    confirm_delete: '确认删除',
    delete_success: '删除成功',
    confirm: '确认',
    cancel: '取消',
    search: '搜索',
    back: '返回',
    view: '视图',
    module: '组件',
    filter_module: '过滤组件',
    select_by_module: '按组件选择',
    edit: '编辑',
    sys_template: '系统模板',
    user_template: '用户模板',
    add_category: '添加分类',
    filter_keywords: '输入关键字进行过滤',
    dashboard_theme: '仪表板主题',
    table: '表格',
    gap: '有间隙',
    no_gap: '无间隙',
    component_gap: '组件间隙',
    refresh_time: '刷新时间',
    minute: '分钟',
    second: '秒',
    photo: '图片',
    default_panel: '默认仪表板',
    create_public_links: '创建公共链接',
    to_default: '另存为默认',
    to_default_panel: '另存为默认仪表板',
    store: '收藏',
    save_to_panel: '保存为模板',
    export_to_panel: '导出为模板',
    export_to_pdf: '导出为PDF',
    preview: '预览',
    fullscreen_preview: '全屏预览',
    new_tab_preview: '新Tab页预览',
    select_panel_from_left: '请从左侧选择仪表板',
    template_nale: '模板名称',
    template: '模板',
    category: '分类',
    all_org: '所有组织',
    custom: '自定义',
    import_template: '导入模板',
    copy_template: '复用模板',
    upload_template: '上传模板',
    belong_to_category: '所属类别',
    pls_select_belong_to_category: '请选择所属类别',
    template_name_cannot_be_empty: '模板名称不能为空',
    select_by_table: '按表选择',
    data_list: '数据列表',
    component_list: '组件列表',
    custom_scope: '自定义控制范围',
    multiple_choice: '多选',
    single_choice: '单选',
    field: '字段',
    unshared_people: '未分享人员',
    shared_people: '已分享人员',
    error_data: '获取数据出错，请联系管理员',
    canvas_size: '画布大小',
    canvas_scale: '画布比例',
    style: '样式',
    clean_canvas: '清空画布',
    insert_picture: '插入图片',
    redo: '重做',
    undo: '撤销',
    panelNull: '这是个空的仪表板，可以通过编辑来丰富内容',
    copy: '复制',
    paste: '粘贴',
    cut: '剪切',
    lock: '锁定',
    topComponent: '置顶',
    bottomComponent: '置底',
    upComponent: '上移',
    downComponent: '下移',
    linkage_setting: '联动设置',
    add_tab: '新增Tab',
    open_aided_design: '打开组件辅助设计',
    close_aided_design: '关闭组件辅助设计',
    open_style_design: '打开样式设计',
    close_style_design: '关闭样式设计',
    matrix_design: '矩阵设计',
    left: 'x 坐标',
    top: 'y 坐标',
    height: '高',
    width: '宽',
    color: '颜色',
    backgroundColor: '背景色',
    borderStyle: '边框风格',
    borderWidth: '边框宽度',
    borderColor: '边框颜色',
    borderRadius: '边框半径',
    fontSize: '字体大小',
    fontWeight: '字体粗细',
    lineHeight: '行高',
    letterSpacing: '字间距',
    padding: '内间距',
    margin: '外间距',
    textAlign: '左右对齐',
    opacity: '不透明度',
    verticalAlign: '上下对齐',
    text_align_left: '左对齐',
    text_align_center: '左右居中',
    text_align_right: '右对齐',
    vertical_align_top: '上对齐',
    vertical_align_middle: '居中对齐',
    vertical_align_bottom: '下对齐',
    border_style_solid: '实线',
    border_style_dashed: '虚线',
    select_component: '请选择组件',
    other_module: '其他',
    content: '内容',
    default_panel_name: '默认仪表板名称',
    source_panel_name: '原仪表板名称',
    content_style: '内容样式',
    canvas_self_adaption: '自适应画布区域',
    panel_save_tips: '仪表板已变动，是否保存？',
    panel_save_warn_tips: '如果未保存，你对仪表板做的变更将会丢失！',
    do_not_save: '不保存',
    save: '保存',
    drill: '下钻',
    linkage: '联动',
    jump: '跳转',
    cancel_linkage: '取消联动',
    remove_all_linkage: '清除所有联动',
    exit_un_march_linkage_field: '存在未匹配联动关系的字段',
    details: '详情',
    setting: '设置',
    no_drill_field: '缺少关联字段',
    matrix: '矩阵',
    suspension: '悬浮',
    new_element_distribution: '当前元素移入分布方式',
    subject_no_edit: '系统主题不能修改',
    subject_name_not_null: '主题名称需要1~20字符',
    is_enable: '是否启用',
    open_mode: '打开方式',
    new_window: '新开页面',
    now_window: '当前页面',
    hyperLinks: '超链接',
    link_open_tips: '仪表板非编辑状态可打开链接',
    data_loading: '数据准备中...',
    export_loading: '导出中...',
    export_pdf: '导出PDF',
    jump_set: '跳转设置',
    enable_jump: '启用跳转',
    column_name: '字段名称',
    enable_column: '启用字段',
    open_model: '打开方式',
    link_type: '链接类型',
    link_outer: '外部链接',
    link_panel: '仪表板',
    select_jump_panel: '选择关联的仪表板',
    link_view: '联动视图',
    link_view_field: '联动视图字段',
    add_jump_field: '追加跳转联动依赖字段',
    input_jump_link: '请输入跳转连接',
    select_dimension: '请选择维度...',
    video_type: '视频类型',
    online_video: '在线视频',
    streaming_media: '流媒体',
    auto_play: '自动播放',
    video_tips: '优先HTTPS链接；当前支持格式mp4,webm',
    play_frequency: '播放频率',
    play_once: '播放一次',
    play_circle: '循环播放',
    video_links: '视频链接',
    video_add_tips: '请点击添加配置视频信息...'
  },
  plugin: {
    local_install: '本地安装',
    remote_install: '远程安装',
    name: '插件名称',
    free: '是否免费',
    cost: '费用',
    descript: '描述',
    version: '版本',
    creator: '作者',
    install_time: '安装时间',
    release_time: '时间',
    un_install: '卸载',
    uninstall_confirm: '确定卸载该插件',
    uninstall_cancel: '取消卸载插件'
  },
  display: {
    logo: '头部系统logo',
    loginLogo: '登录页面头部logo',
    loginImage: '登录页面右侧图片',
    loginTitle: '登录页面标题',
    title: '系统名称',
    advice_size: '建议图片大小',
    theme: '主题颜色',

    topMenuColor: '头部背景',
    topMenuActiveColor: '头部选中背景',

    topMenuTextColor: '头部字体颜色',

    topMenuTextActiveColor: '头部字体选中颜色',

    themeLight: '浅色',
    themeDark: '深色',
    themeCustom: '自定义'

  },
  auth: {
    authConfig: '权限配置',
    authQuickConfig: '权限快捷配置',
    dept: '组织',
    role: '角色',
    user: '用户',
    linkAuth: '数据源权限',
    datasetAuth: '数据集权限',
    chartAuth: '视图权限',
    panelAuth: '仪表板权限',
    menuAuth: '菜单和操作权限',
    deptHead: '所有组织',
    roleHead: '所有角色',
    userHead: '所有用户',
    linkAuthHead: '所有数据源',
    datasetAuthHead: '所有数据集',
    chartAuthHead: '所有视图',
    panelAuthHead: '所有仪表板',
    menuAuthHead: '所有菜单和操作',
    view: '查看',
    use: '使用',
    export: '导出',
    manage: '管理'
  },
  about: {
    auth_to: '授权给',
    invalid_license: 'License 无效',
    update_license: '更新 License',
    expiration_time: '过期时间',
    expirationed: '(已过期)',
    auth_num: '授权数量',
    version: '版本',
    version_num: '版本号',
    standard: '标准版',
    enterprise: '企业版',
    suport: '获取技术支持',
    update_success: '更新成功'
  },
  template: {
    exit_same_template_check: '当前分类存在相同名称模板，是否覆盖？',
    override: '覆盖',
    cancel: '取消',
    confirm_upload: '上传确认'
  },
  cron: {
    second: '秒',
    minute: '分',
    hour: '时',
    day: '日',
    minute_default: '分 (执行时间：0秒)',
    hour_default: '时 (执行时间：0分0秒)',
    day_default: '日 (执行时间：0时0分0秒)',
    month: '月',
    week: '周',
    year: '年',
    d_w_cant_not_set: '日期与星期不可以同时为“不指定”',
    d_w_must_one_set: '日期与星期必须有一个为“不指定”',
    every_day: '每日',
    cycle: '周期',
    not_set: '不指定',
    from: '从',
    to: '至',
    repeat: '循环',
    day_begin: '日开始，每',
    day_exec: '日执行一次',
    work_day: '工作日',
    this_month: '本月',
    day_near_work_day: '号，最近的工作日',
    this_week_last_day: '本月最后一天',
    set: '指定',
    every_hour: '每时',
    hour_begin: '时开始，每',
    hour_exec: '时执行一次',
    every_month: '每月',
    month_begin: '月开始，每',
    month_exec: '月执行一次',
    every: '每',
    every_begin: '开始，每',
    every_exec: '执行一次',
    every_week: '每周',
    week_start: '从星期',
    week_end: '至星期',
    every_year: '每年',
    week_tips: '说明：1-7 分别对应 周日-周六',
    minute_limit: '分钟不能小于1，大于59',
    hour_limit: '小时不能小于1，大于23',
    day_limit: '天不能小于1，大于31'
  },
  dept: {
    can_not_move_change_sort: '不能移动以改变排序',
    can_not_move_parent_to_children: '父组织不能移动到自己的子节点下',
    move_success: '移动成功',
    name_exist_pre: '下已存在名称为【',
    name_exist_suf: '】的组织',
    root_org: '顶层组织'
  },
  webmsg: {
    web_msg: '站内消息通知',
    show_more: '查看更多',
    all_type: '全部类型',
    panel_type: '仪表板分享',
    dataset_type: '数据集同步',
    content: '消息内容',
    sned_time: '提交时间',
    read_time: '查看时间',
    type: '消息类型',
    mark_readed: '标记已读',
    all_mark_readed: '全部已读',
    please_select: '请至少选择一条消息',
    mark_success: '标记已读成功',
    receive_manage: '接收管理',
    i18n_msg_type_panel_share: '仪表板分享',
    i18n_msg_type_panel_share_cacnel: '仪表板取消分享',
    i18n_msg_type_dataset_sync: '数据集同步',
    i18n_msg_type_dataset_sync_success: '数据集同步成功',
    i18n_msg_type_dataset_sync_faild: '数据集同步失败',
    i18n_msg_type_ds_invalid: '数据源失效',
    i18n_msg_type_all: '全部类型',
    channel_inner_msg: '站内消息',
    channel_email_msg: '邮件'
  },
  denumberrange: {
    label: '数值区间',
    split_placeholder: '至',
    please_key_min: '请输入最小值',
    please_key_max: '请输入最大值',
    out_of_min: '最小值不能小于最小整数-2³²',
    out_of_max: '最大值不能大于最大整数2³²-1',
    must_int: '请输入整数',
    min_out_max: '最小值必须小于最大值',
    max_out_min: '最大值必须大于最小值'
  },
  denumberselect: {
    label: '数字下拉',
    placeholder: '请选择'
  },
  deinputsearch: {
    label: '文本搜索',
    placeholder: '请输入关键字'
  },
  detextselect: {
    label: '文本下拉',
    placeholder: '请选择'
  },
  detextgridselect: {
    label: '文本列表',
    placeholder: '请选择'
  },
  denumbergridselect: {
    label: '数字列表',
    placeholder: '请选择'
  },
  dedaterange: {
    label: '日期范围',
    to_placeholder: '结束日期',
    from_placeholder: '开始日期',
    split_placeholder: '至'
  },
  dedate: {
    label: '日期',
    placeholder: '请选择日期'
  },
  deyearmonth: {
    label: '年月',
    placeholder: '请选择年月'
  },
  deyear: {
    label: '年份',
    placeholder: '请选择年份'
  },
  deshowdate: {
    label: '时间',
    show_week: '显示星期',
    show_date: '显示日期',
    time_format: '时间格式',
    date_format: '日期格式',
    custom: '自定义格式',
    open_mode: '展示风格',
    m_default: '简单风格',
    m_elec: '电子时钟',
    m_simple: '简单表盘',
    m_complex: '复杂表盘',
    select_openMode: '请选择展示风格',
    select_time_format: '请选择时间格式',
    select_date_format: '请选择日期格式'

  }
}
