<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-16 14:03:21
 * @Description: 中医-数据中台-数据统计-分店数据统计
 * @type: page
-->

<template>
	<el-card class="page-container">
		<Form class="demo-form-inline" ref="searchForm" :inline="true" :model="searchForm" :items="searchFormItems"
			:buttons="operationBtns" @search="handleSearch"/>

		<Table class="g-table-container" :tableData="table.tableList" :columns="table.columns" :total="table.total"
			:pagination="table.pagination" :loading="table.isTableLoading" :sizeChange="handleSizeChange"
			:currentChange="handleCurrentChange" />

	</el-card>
</template>
<script>

import Form from "@/components/Combinecom/form";
import Table from "@/components/Combinecom/table";
import API from "@/api/dataCenter";
import { handleFormatMoney } from '@/utils/validate'

export default {
	name: "moneyAccount",
	components: { Form, Table },
	computed: {
		// 查询表单项
		searchFormItems() {
			return [
				{
					type: "text",
					size: "small",
					placeholder: "请输入店铺名称",
					label: "名称",
					value: "searchName",
					limitLength: 20,
					clearable: true,
				},
			];
		},
		// 查询区按钮配置项
		operationBtns() {
			return [
				{
					class: "is-plain",
					type: "primary",
					size: "small",
					label: "搜索",
					icon: "el-icon-search",
					click: () => this.handleSearch()
				},
				{
					class: "is-plain",
					type: "info",
					size: "small",
					label: "重置",
					icon: "el-icon-refresh-left",
					click: () => this.handleReset(),
				},
			];
		},
	},
	data() {
		return {
			// 查询条件栏
			searchForm: {
				searchName: null,
				companyId: currentUser.company.id
			},
			// 表数据
			table: {
				// table参数
				total: 0,
				tableList: [],
				pagination: {
					limit: 20,
					offset: 0
				},
				columns: [
					{
						label: "序号",
						type: "index",
					},
					{
						label: "店铺名称",
						prop: "company.name"
					},
					{
						label: "当日营业额(元)",
						prop: "todayIncome",
						formatter: row => {
							return handleFormatMoney(row.todayIncome)
						}
					},
					{
						label: "总计营业额(元)",
						prop: "totalIncome",
						formatter: row => {
							return handleFormatMoney(row.totalIncome)
						}
					},
					{
						label: "奖金分配总额(元)",
						prop: "totalBonusAmt",
						formatter: row => {
							return handleFormatMoney(row.totalBonusAmt)
						}
					},
					{
						label: "资金结算总额(元)",
						prop: "totalSettleAmt",
						formatter: row => {
							return handleFormatMoney(row.totalSettleAmt)
						}
					},
					{
						label: '操作',
						minWidth: 120,
						buttons: [
							{
								label: '详情',
								type: 'text',
								// disabled: true,
								click: row => this.handleCheckDetail(row),
							},
						]
					}
				],
				isTableLoading: false // 表格loading
			},
		};
	},
	mounted() {
		this.getTableData()
	},
	methods: {
		// total
		handleSizeChange(val) {
			// console.log('val', val)
			this.table.pagination.offset = val;
			this.getTableData();
		},
		// 改变页数
		handleCurrentChange(val) {
			// console.log('val2222', val)
			this.table.pagination.offset = val;
			this.getTableData();
		},
		// 查询
		handleSearch() {
			this.getTableData()
		},
		// 重置
		handleReset() {
			this.searchForm.searchName = null
			this.table.pagination.offset = 0

			this.getTableData()
		},

		// 详情
		handleCheckDetail(row) {
			// console.log('跳转到详情页', row)
			this.$router.push({
				path: '/singleStatisticsDetail',
				query: { id: row.company.id }
			})
		},

		// table数据
		async getTableData() {
			const data = {
				...this.searchForm,
				limit: this.table.pagination.limit,
				offset: this.table.pagination.offset ? (this.table.pagination.offset - 1) * this.table.pagination.limit : 0,
			};
			// console.log('data', data)
			// return
			try {
				this.table.isTableLoading = true;
				const res = await API.reqSingleList(data);
				if (res.code === '100') {
					// 如果当前页无数据且不是第一页，则返回第一页
					if (!res.data.rows.length && this.table.pagination.offset > 0) {
						this.table.pagination.offset = 0;
						this.getTableData()
					}
					// console.log("xiangfei", res.data);
					this.table.tableList = res.data.rows || [];
					this.table.total = Number(res.data.total);
				} else {
					this.$message.warning(res.msg);
				}
				this.table.isTableLoading = false;
			} catch (error) {
				this.table.isTableLoading = false;
				this.$message.error(error);
			}
		}
	},
};
</script>

<style lang="scss" scoped>
.tagStyle {
	text-align: center;
}
</style>