<!--
 * @Author: x<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-16 14:03:21
 * @Description: 中医-数据中台-奖金分配
 * @type: page
-->

<template>
	<el-card class="page-container">
		<Form class="demo-form-inline" ref="searchForm" :inline="true" :model="searchForm" :items="searchFormItems"
			:buttons="operationBtns" @search="handleSearch" />

		<Table class="g-table-container" :tableData="table.tableList" :columns="table.columns" :total="table.total"
			:pagination="table.pagination" :loading="table.isTableLoading" :sizeChange="handleSizeChange"
			:currentChange="handleCurrentChange" :selectedChange="handleSlectedChange">
			<el-table-column slot="approvalStatus" prop="approvalStatus" label="审核状态">
				<template slot-scope="scope">
					<div class="tagStyle">
						<el-tag :type="Number(scope.row.approvalStatus) === 2 ? 'success' : 'warning'">{{
							Number(scope.row.approvalStatus)
								=== 2 ? '已审核' :
								'待审核'
						}}</el-tag>
					</div>
				</template>
			</el-table-column>
			<el-table-column slot="settlementStatus" prop="settlementStatus" label="发放状态">
				<template slot-scope="scope">
					<div class="tagStyle">
						<el-tag :type="Number(scope.row.settlementStatus) === 2 ? 'success' : 'warning'">{{
							Number(scope.row.settlementStatus) === 2 ? '已发放' : '待发放'
						}}</el-tag>
					</div>
				</template>
			</el-table-column>
		</Table>
	</el-card>
</template>

<script>

import Form from "@/components/Combinecom/form";
import Table from "@/components/Combinecom/table";
import Options from '../options'
import API from "@/api/dataCenter";
import { handleFormatMoney } from '@/utils/validate'

export default {
	// extends: MainUI,
	name: "moneyShare",
	components: { Form, Table },
	computed: {
		// 查询表单项
		searchFormItems() {
			return [
				{
					type: "text",
					size: "small",
					placeholder: "输入分店名称或客户名称",
					label: "名称",
					value: "searchName",
					limitLength: 20,
					clearable: true,
				},
				{
					type: "select",
					size: "small",
					placeholder: "请选择发放状态",
					label: "发放状态",
					value: "status",
					filterable: true,
					// clearable: true,
					change: this.handleChangeStatus,
					options: Options.sendStatusList
				},
				{
					type: "datetimerange",
					size: "small",
					label: "选择时间",
					placeholder: "请选择日期",
					value: "rangeTime",
					valueFormat: "yyyy-MM-dd HH:mm:ss"
				}
			];
		},
		// 查询区按钮配置项
		operationBtns() {
			return [
				{
					class: "is-plain",
					type: "primary",
					size: "small",
					label: "搜索",
					icon: "el-icon-search",
					click: () => this.handleSearch()
				},
				{
					class: "is-plain",
					type: "info",
					size: "small",
					label: "重置",
					icon: "el-icon-refresh-left",
					click: () => this.handleReset(),
				},
				// {
				// 	type: "primary",
				// 	size: "small",
				// 	label: "添加",
				// 	icon: "el-icon-plus",
				// 	click: () => this.handleAdd()
				// },
			];
		},
	},
	data() {
		return {
			// 查询条件栏
			searchForm: {
				searchName: null,
				status: null,
				startDate: null,
				endDate: null,
				rangeTime: null,
				// companyId: currentUser.company.id
				companyId: currentUser.company.id

			},
			// 表数据
			table: {
				// table参数
				total: 0,
				tableList: [],
				pagination: {
					limit: 20,
					offset: 0
				},
				columns: [
					{ type: "selection" },
					{
						label: "分店名称",
						prop: "company.name"
					},
					{
						label: "客户名称",
						prop: "memberManagement.name"
					},
					{
						label: "奖金金额(元)",
						prop: "settlementAmount",
						formatter: row => {
							// console.log('row', row)
							return handleFormatMoney(row.settlementAmount)
						}
					},
					{
						slot: "approvalStatus"
					},
					{
						slot: "settlementStatus"
					},
					{
						label: "发放时间",
						prop: "settlementDate",
					},
					{
						label: '操作',
						minWidth: 120,
						buttons: [
							{
								label: '审核',
								type: 'text',
								// disabled: true,
								click: row => this.handleCheck(row),
								hidden: row => Number(row.approvalStatus) === 2
							},
							{
								label: '发放',
								type: 'text',
								// disabled: true,
								click: row => this.handleSend(row),
								hidden: row => Number(row.approvalStatus) === 1 || Number(row.settlementStatus) === 2
							},
						]
					}
				],
				isTableLoading: false // 表格loading
			},
			// 选中id列表
			selectedIdsList: []
		};
	},
	mounted() {
		this.getTableData()
	},
	methods: {
		// 多选
		handleSlectedChange(val) {
			// console.log('val', val)
			this.selectedIdsList = val.map(v => v.id);
		},
		// total
		handleSizeChange(val) {
			this.table.pagination.offset = val;
			this.getTableData();
		},
		// 改变页数
		handleCurrentChange(val) {
			this.table.pagination.offset = val;
			this.getTableData();
		},
		// 查询
		handleSearch() {
			this.getTableData()
		},
		// 重置
		handleReset() {
			this.searchForm.searchName = null
			this.searchForm.status = null
			this.searchForm.rangeTime = null
			this.table.pagination.offset = 0

			this.getTableData()
		},
		// 审核
		handleCheck(row) {
			// console.log('审核row', row)
			this.$confirm('该操作不可撤销，确认审核选中的数据吗？', '确认', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				this.getCheckData(row)
			}).catch(() => {
				this.$message.info("已取消")
			})
		},
		// 发放
		handleSend(row) {
			// console.log('发放row', row)
			this.$confirm('该操作不可撤销，确认发放选中的数据吗？', '确认', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				this.getSendData(row)
			}).catch(() => {
				this.$message.info("已取消")
			})
		},


		// 审核
		async getCheckData(row) {
			let ids = []
			// 单选
			if (!this.selectedIdsList.length) {
				ids = [row.id]
			} else {
				// 多选
				ids = this.selectedIdsList
			}
			// console.log('ids', ids)
			// return

			try {
				const res = await API.reqCheckData(ids);
				if (res.code === '100') {
					this.$message.success('审核成功!');
					this.getTableData()
				} else {
					this.$message.warning(res.msg);
				}
			} catch (error) {
				this.$message.error(error);
			}

		},

		// 发放
		async getSendData(row) {
			let ids = []
			// 单选
			if (!this.selectedIdsList.length) {
				ids = [row.id]
			} else {
				// 多选
				ids = this.selectedIdsList
			}


			try {
				const res = await API.reqSendData(ids);
				if (res.code === '100') {
					this.$message.success('发放成功!');
					this.getTableData()
				} else {
					this.$message.warning(res.msg);
				}
			} catch (error) {
				this.$message.error(error);
			}

		},

		// table数据
		async getTableData() {
			const data = {
				...this.searchForm,
				startDate: this.searchForm.rangeTime ? this.searchForm.rangeTime[0] : "",
				endDate: this.searchForm.rangeTime ? this.searchForm.rangeTime[1] : "",
				limit: this.table.pagination.limit,
				offset: this.table.pagination.offset ? (this.table.pagination.offset - 1) * this.table.pagination.limit : 0,
			};

			delete data.rangeTime;
			// console.log('data', data)
			// return
			try {
				this.table.isTableLoading = true;
				const res = await API.reqMoneyShareList(data);
				// console.log("res", res);
				if (res.code === '100') {
					// 如果当前页无数据且不是第一页，则返回第一页
					if (!res.data.rows.length && this.table.pagination.offset > 0) {
						this.table.pagination.offset = 0;
						this.getTableData()
					}

					this.table.tableList = res.data.rows || [];
					this.table.total = Number(res.data.total);
				} else {
					this.$message.warning(res.msg);
				}
				this.table.isTableLoading = false;
			} catch (error) {
				this.table.isTableLoading = false;
				this.$message.error(error);
			}
		}
	},
};
</script>

<style lang="scss" scoped>
.tagStyle {
	text-align: center;
}
</style>