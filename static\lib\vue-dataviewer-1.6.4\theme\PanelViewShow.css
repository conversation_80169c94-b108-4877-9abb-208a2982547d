
.root_class[data-v-5e8dba4b] {
  margin: 15px 0px 5px;
  text-align: center;
}
.export_body_class[data-v-5e8dba4b]{
  border: 1px solid #dcdfe6 ;
  height: 65vh;
  overflow-y: auto;
}
.export_body_inner_class[data-v-5e8dba4b]{
  margin: 10px;
}
.my-container[data-v-5e5021aa]{position:absolute;overflow:auto;top:0px;right:0px;bottom:0px;left:0px}.ccondition-main[data-v-5e5021aa]{position:absolute;overflow:auto;top:0px;right:0px;bottom:0px;left:0px}.condition-title[data-v-5e5021aa]{top:0px;right:0px;bottom:0px;left:0px;position:absolute;height:2em;cursor:-webkit-grab}.first-title[data-v-5e5021aa]{width:100%;overflow:hidden;position:absolute;color:inherit;display:flex;align-items:center}.condition-title-absolute[data-v-5e5021aa]{right:0px;bottom:0px;position:absolute;top:0px;left:4px;display:flex;align-items:flex-end}.span-container[data-v-5e5021aa]{overflow:hidden auto;position:relative;padding:0 5px}.condition-content[data-v-5e5021aa]{overflow:auto hidden;top:2em;left:0px;right:0px;bottom:0px;position:absolute;letter-spacing:0px !important}.condition-content-container[data-v-5e5021aa]{position:relative;display:table;width:100%;height:100%;white-space:nowrap}.first-element[data-v-5e5021aa]{position:relative;display:table-cell;vertical-align:middle;margin:0px;padding:0px;height:100%}.first-element-contaner[data-v-5e5021aa]{width:calc(100% - 10px);background:initial;position:absolute;bottom:5px;margin:0 4px;display:flex;align-items:flex-end}.first-element-contaner div[data-v-5e5021aa]{width:100%}.first-element-grid-contaner[data-v-5e5021aa]{background:#fff;border:1px solid #d7dae2;top:5px}.condition-main-line[data-v-5e5021aa]{height:40px !important}.condition-content-default[data-v-5e5021aa]{inset:0px 0px 0px !important}.slot-class[data-v-197ec156]{color:#fff}.bottom[data-v-197ec156]{margin-top:20px;text-align:center}.ellip[data-v-197ec156]{margin-left:10px;margin-right:10px;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;background-color:#f7f8fa;color:#3d4d66;font-size:12px;line-height:24px;height:24px;border-radius:3px}.select-filed[data-v-197ec156]{margin-left:10px;margin-right:10px;overflow:hidden;white-space:nowrap;text-overflow:ellipsis;color:#3d4d66;font-size:12px;line-height:35px;height:35px;border-radius:3px}[data-v-197ec156] .el-popover{height:200px;overflow:auto}.bar-main[data-v-df7f0e8e]{position:absolute;right:0px;float:right;z-index:2;border-radius:2px;padding-left:5px;padding-right:2px;cursor:pointer !important;background-color:#0a7be0}.bar-main i[data-v-df7f0e8e]{color:#fff;float:right;margin-right:3px}.bar-main[data-v-30ef83ca]{position:absolute;right:0px;float:right;z-index:2;border-radius:2px;padding-left:5px;padding-right:2px;cursor:pointer !important;background-color:#0a7be0}.bar-main i[data-v-30ef83ca]{color:#fff;float:right;margin-right:3px}.component[data-v-413b6676]{position:absolute}.component[data-v-413b6676]:hover{box-shadow:0px 0px 3px #0a7be0}.gap_class[data-v-413b6676]{padding:5px}.component-custom[data-v-413b6676]{outline:none;width:100% !important;height:100%}.menu-item[data-v-7efcd2ad]{font-size:12px}[data-v-7efcd2ad] ul{width:80px}.track-menu[data-v-7efcd2ad]{border:#3a8ee6 1px solid}
.map-zoom-box[data-v-c3749546] {
  position: absolute;
  z-index: 999;
  left: 2%;
  bottom: 30px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  text-align: center;
  padding: 2px;
  border-radius: 5px
}


.table-class[data-v-fbc27634] .body--wrapper{
  background: rgba(1,1,1,0);
}
.table-class[data-v-fbc27634] .elx-cell{
  max-height: none!important;
  line-height: normal!important;
}
.table-page[data-v-fbc27634]{
  position: absolute;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  width: 100%;
  overflow: hidden;
}
.page-style[data-v-fbc27634]{
  margin-right: auto;
}
.total-style[data-v-fbc27634]{
  flex: 1;
  font-size: 12px;
  color: #606266;
  white-space:nowrap;
}
.page-style[data-v-fbc27634] .el-input__inner{
  height: 24px;
}

.table-class[data-v-381a9d31] .body--wrapper{
  background: rgba(1,1,1,0);
}

.ms-main-container[data-v-3c56fa40] {
  padding: 10px;
  height: calc(100vh - 56px);
}


.ms-container[data-v-08696b79] span.title {
  font-size: 16px;
  font-weight: 500;
  margin-top: 0;
  text-overflow: ellipsis;
  overflow: hidden;
  word-wrap: break-word;
  white-space: nowrap;
}


.drag-bar[data-v-0ce184f6] {
  height: 100%;
  width: 1px;
  position: absolute;
  right: 0px;
  top: 0;
  cursor: col-resize;
  background-color: #E6E6E6;;
  border: 0px;
}
.blackTheme .drag-bar[data-v-0ce184f6] {
  height: 100%;
  width: 1px;
  position: absolute;
  right: 0px;
  top: 0;
  cursor: col-resize;
  background-color: var(--SiderTextColor) !important;
  border: 0px;
}
.drag-bar[data-v-0ce184f6]:hover {
  width: 3px;
}


.ms-aside-container[data-v-0dc6f361] {
  /* border: 1px solid #E6E6E6; */
  padding: 10px;
  border-radius: 2px;
  box-sizing: border-box;
  background-color: var(--SiderBG, #FFF);
  height: calc(100vh - 56px);
  border-right: 0px;
  position: relative;
}
.hiddenBottom[data-v-0dc6f361] {
  width: 8px;
  height: 50px;
  top: calc((100vh - 80px)/3);
  right: -10px;
  /*top: 0;*/
  line-height: 50px;
  border-radius: 0 15px 15px 0;
  background-color: #acb7c1;
  display: inline-block;
  position: absolute;
  cursor: pointer;
  opacity: 0.2;
  font-size: 2px;
  margin-left: 1px;
}
.hiddenBottom i[data-v-0dc6f361] {
  margin-left: -2px;
}
.hiddenBottom[data-v-0dc6f361]:hover {
  background-color: #783887;
  opacity: 0.8;
  width: 12px;
}
.hiddenBottom:hover i[data-v-0dc6f361] {
  margin-left: 0;
  color: white;
}

.ms-aside-container[data-v-7c3a8ac2]{height:50vh;min-width:400px;max-width:400px;padding:0 0}.ms-main-container[data-v-7c3a8ac2]{height:50vh;border:1px solid #e6e6e6;border-left:0 solid}.chart-class[data-v-7c3a8ac2]{height:100%}.table-class[data-v-7c3a8ac2]{height:100%}.bar-main[data-v-22891710]{position:absolute;right:0px;z-index:10;height:20px;border-radius:2px;padding-left:5px;padding-right:2px;cursor:pointer !important;opacity:.8}.ms-main-container[data-v-40400cbe]{border:0px}.chart-class[data-v-40400cbe]{height:100%}.table-class[data-v-40400cbe]{height:100%}.full-div[data-v-40400cbe]{background-size:100% 100% !important}.bg[data-v-32145950]{min-width:200px;min-height:300px;width:100%;height:100%;overflow-x:hidden;background-size:100% 100% !important}.main-class[data-v-32145950]{width:100%;height:100%;background-size:100% 100% !important}.custom-position[data-v-32145950]{height:100%;flex:1;display:flex;align-items:center;justify-content:space-between;font-size:14px;flex-flow:row nowrap;color:#9ea6b2}.dialog-css[data-v-32145950] .el-dialog__title{font-size:14px}.dialog-css[data-v-32145950] .el-dialog__header{padding:20px 20px 0}.dialog-css[data-v-32145950] .el-dialog__body{padding:10px 20px 20px}.mobile-dialog-css[data-v-32145950] .el-dialog__body{padding:0px}[data-v-32145950]::-webkit-scrollbar{width:0px !important;height:0px !important}[data-v-32145950] .el-tabs__nav{z-index:0}
.view-list {
  height: 100%;
  width: 20%;
  min-width: 180px;
  max-width: 220px;
  border: 1px solid #E6E6E6;
  border-left: 0 solid;
  overflow-y: auto;
}
.view-list-thumbnails-outline {
  height: 100%;
  overflow-y: auto;
}
.view-list-thumbnails {
  width: 100%;
  padding: 0px 15px 15px 0px;
}
.panel-design {
  min-height: 400px;
  height: 100%;
  min-width: 500px;
  overflow-y: hidden;
  /* border-top: 1px solid #E6E6E6; */
}
.panel-design-head {
  height: 40px;
  background-color: var(--SiderBG, white);
  padding: 0 0 0 40px;
  line-height: 40px;
}
.panel-share-head {
  height: auto !important;
}
.blackTheme .panel-design-head  {
  color: var(--TextActive);
}
.panel-design-preview {
  width: 100%;
  height: calc(100% - 40px);
  overflow-x: hidden;
  overflow-y: auto;
  /*padding: 5px;*/
}
.custom-position {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  flex-flow: row nowrap;
  color: #9ea6b2;
}
.dialog-css2 ::v-deep .el-dialog__title {
  font-size: 14px!important;
}
.dialog-css2 ::v-deep .el-dialog__header {
  padding: 20px 20px 0!important;
}
.dialog-css2 ::v-deep .el-dialog__body {
  padding: 0px 20px!important;
}
