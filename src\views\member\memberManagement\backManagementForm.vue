<!--
 * @Author: x<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-19 17:44:02
 * @Description: 中医-更多-会员管理-退卡-弹框
 * @type: component
-->

<template>
	<el-dialog :title='dialogProps.title' :visible.sync='dialogProps.visible' v-if="dialogProps.visible"
		:close-on-click-modal='false' width='80%' @open='handleOpen()' v-loading='loading'>

		<el-form :model='formData' :rules='formRules' ref='form' label-width='210px' label-position='right'
			class='edit-form'>
			<div class="tab-item">
				<el-row>
					<el-col :span='12'>
						<el-form-item label='赠送总金额(元)'>
							{{ formData.creditsSum || '-' }}
						</el-form-item>
					</el-col>
					<el-col :span='12'>
						<el-form-item label='公司退给用户的金额(元)'>
							{{ formData.userRefundSum || '-' }}
						</el-form-item>
					</el-col>
				</el-row>
				<el-row>
					<el-col :span='12'>
						<el-form-item label='分支退给公司的结算总金额(元)'>
							{{ formData.companyRefundSum || '-' }}
						</el-form-item>
					</el-col>
					<el-col :span='12'>
						<el-form-item label='会员卡余额(元)' prop="money">
							{{ formData.money || '-' }}
						</el-form-item>
					</el-col>
				</el-row>
				<el-row>
					<el-col :span='12'>
						<el-form-item label='结算状态'>
							{{ formData.refundStatus === '1' ? '已结算' : '未结算' || '-' }}
						</el-form-item>
					</el-col>
					<el-col :span='12'>
						<el-form-item label='结算时间' prop="createTime">
							{{ formData.createTime || '-' }}
						</el-form-item>
					</el-col>
				</el-row>

				<el-row>
					<el-col :span='12'>
						<el-form-item label='审批状态' prop="checkStatus">
							{{ formData.checkStatus === '1' ? '已审核' : '未审批' || '-' }}
						</el-form-item>
					</el-col>
					<el-col :span='12'>
						<el-form-item label='银行账号' prop="account">
							<el-input v-model='formData.account' placeholder="请输入银行账号"></el-input>
						</el-form-item>
					</el-col>
				</el-row>

				<div v-if='formData.refundSettleDetails
					&& formData.refundSettleDetails
						.length >= 1'>
					<h3>退款结算明细</h3>
					<el-table :data="formData.refundSettleDetails
						&& formData.refundSettleDetails" style="width: 100%">
						<el-table-column prop="companyId" label="诊所id">
						</el-table-column>
						<el-table-column prop="refundMoney" label="分支退给公司的结算金额(元)">
						</el-table-column>
						<el-table-column prop="refundStatus" label="结算状态">
							<template slot-scope="scope">
								<span>
									{{ scope.row.refundStatus === '1' ? '已结算' : '未结算' }}
								</span>
							</template>
						</el-table-column>
						<el-table-column prop="createTime" label="结算时间">
						</el-table-column>

					</el-table>
				</div>
			</div>
		</el-form>

		<!-- 确认和取消 -->
		<span slot='footer' class='dialog-footer'>
			<el-button type='primary' :plain='true' :loading="isConformLoading" @click="handleSubmit">确认退卡</el-button>
			<el-button :plain='true' @click='handleClose()'>取 消</el-button>
		</span>
	</el-dialog>
</template>
<script>

import { getBackManagementConfirm } from '@/api/member/memberManagement'
import { handleFormatMoney } from '@/utils/validate'
import BaseUI from '@/views/components/baseUI'
import OperationIcon from '@/components/OperationIcon'

export default {
	extends: BaseUI,
	name: 'memberManagement-form',
	components: {
		OperationIcon
	},
	data() {
		return {
			dialogProps: {
				visible: false,
				action: '',
				title: ''
			},
			formData: {},
			formRules: {
				account: [
					{ required: true, message: '请输入银行账号', trigger: 'change' }
				],
			},

			chargeMainTablePagesize: 20 /*消费页大小*/,
			chargeMainTableCurrentPage: 1 /*消费主表页*/,
			chargeMainTableTotal: 0 /*消费总数据量*/,
			chargeTableData: [], // 充值记录
			consumeTableData: [], // 消费记录table 

			isConformLoading: false // 确认退卡loading
		}
	},
	methods: {
		// 获取支付方式名 
		handlePayTypeText(type) {
			let val = ""
			switch (type) {
				case "payType_0":
					val = '现金';
					break;
				case "payType_1":
					val = '支付宝';
					break;
				case "payType_2":
					val = '微信';
					break;
				case "payType_3":
					val = '银行卡';
					break;
				case "payType_4":
					val = '医保';
					break;
				case "payType_5":
					val = '会员卡';
					break;
				default:
					val = "";
					break;
			}
			return val;

		},
		// 消费页数
		handleSizeChangeMain(value) {
			this.chargeMainTablePagesize = value
			this.getConsumeRecord()
		},
		// 消费页码
		handlePageChangeMain(value) {
			this.chargeMainTableCurrentPage = value
			this.getConsumeRecord()
		},
		// 关闭弹框
		handleClose() {
			this.dialogProps.visible = false
		},
		// 打开弹框
		handleOpen() {
			this.$nextTick(() => {
				this.$refs['memberManagementForm'].clearValidate()
			})
		},
		// 确认校验
		handleSubmit() {
			this.$refs.form.validate(valid => {
				if (!valid) return

				this.$confirm(`请仔细核对(当前银行卡号：<span style='color:#FF0000'>${this.formData.account}</span>)!该操作不可撤销，确认退还当前卡吗？`, '确认', {
					cancelButtonText: '取消',
					confirmButtonText: '确定',
					dangerouslyUseHTMLString: true,
					type: 'warning'
				}).then(() => {
					this.handleBackCard()
				}).catch(() => {
					this.$message.info("取消退卡！");
				})

			});
		},

		// 确认退卡
		async handleBackCard() {
			const data = {
				id: this.formData.id,
				companyId: this.formData.companyId,
				account: this.formData.account
			}
			// console.log('data', data)
			// return
			this.isConformLoading = true
			this.setLoad()
			try {
				let res = await getBackManagementConfirm(data)

				if (res.code === '100') {
					this.$message.success("退卡成功！");
					this.handleClose()
					this.$emit('closeDialog')
				} else {
					this.showMessage(res)
				}
				this.isConformLoading = false
				this.resetLoad()
			} catch (error) {
				this.isConformLoading = false
				this.outputError(error)
			}

		},

	},
	mounted: function () {
		this.$nextTick(() => {
			this.$on('openBackManagementDialog', function (data, row) {
				console.log('row', row)
				this.dialogProps.visible = true
				this.dialogProps.title = '退卡明细'

				this.formData = {
					id: row.id,
					companyId: currentUser.company.id,
					money: handleFormatMoney(row.money),
					creditsSum: handleFormatMoney(data.creditsSum),
					userRefundSum: handleFormatMoney(data.userRefundSum),
					companyRefundSum: handleFormatMoney(data.companyRefundSum),
					refundSettleDetails: data.refundSettleDetails.map(item => ({
						...item,
						refundMoney: handleFormatMoney(item.refundMoney)
					}))
				}
				this.formData.money = handleFormatMoney(row.money)
				console.log('formData', this.formData)

			})
		})
	}
}
</script>