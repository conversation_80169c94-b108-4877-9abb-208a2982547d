<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-19 10:25:58
 * @Description: 中医-全局公共组件-弹框
 * @type: component
-->

<template>
	<el-dialog v-bind="$attrs" :class="{ 'vertical-center': verticalCenter }" :close-on-click-modal="false"
		v-on="$listeners">
		<!-- Dialog 的内容 -->
		<slot />
		<!-- slot title -->
		<template #title>
			<!-- Dialog 标题区的内容 -->
			<slot name="title" />
		</template>
		<!-- footer -->
		<template #footer v-if="showButtons">
			<template v-if="buttons && buttons.length">
				<el-button plain v-for="(button, buttonIndex) in getRenderButtons()" :key="buttonIndex" :type="button.type"
					v-bind="button" :disabled="typeof button.disabled === 'function'
						? button.disabled()
						: button.disabled" @click="button.click($event, button, buttonIndex)">
					{{ button.label }}
				</el-button>
			</template>
			<template v-else>
				<el-button size="small" plain @click="cancel">
					取 消
				</el-button>
				<el-button type="primary" plain size="small" :loading="loading" @click="submit">
					{{ submitText }}
				</el-button>
			</template>
		</template>
	</el-dialog>
</template>

<script>
import { isFunction, isBoolean } from '@/utils'

export default {
	name: 'ScDialog',
	inheritAttrs: false,
	props: {
		// 对话框类型，'add'和'edit'是显示按钮操作区
		type: {
			type: String,
			// 'add'/'edit'/任意
			default: '',
		},
		// dialog 垂直居中展示
		verticalCenter: {
			type: Boolean,
		},
		// submit按钮是否加载中状态
		loading: {
			type: Boolean,
		},
		// submit按钮文字
		submitText: {
			type: String,
			default: '保 存',
		},
		// 不显示任何按钮
		showButtons: {
			type: Boolean,
			default: true,
		},
		// 自定义 buttons
		buttons: {
			type: Array,
			default: null,
		},
	},
	methods: {
		getRenderButtons() {
			const buttons = []
			for (let i = 0; i < this.buttons.length; i++) {
				const item = this.buttons[i]
				let render = true
				if (isFunction(item.ifRender)) {
					render = item.ifRender()
				} else if (isBoolean(item.ifRender)) {
					render = item.ifRender
				}
				if (render) {
					buttons.push(item)
				}
			}
			return buttons
		},
		cancel() {
			// cancel按钮回调
			this.$emit('cancel')
		},
		submit() {
			// submit按钮回调
			this.$emit('submit')
		},
	},
}
</script>

<style lang="scss" scoped>
.vertical-center {
	display: flex;
	flex-direction: row;
	justify-content: center;
	align-items: center;


}

/deep/ .el-dialog {
	margin: 0 !important;

	.el-dialog__header {
		padding: 20px 20px 10px;

		.dialog-header {
			width: 100%;
			font-weight: bold;
			padding-bottom: 8px;
			border-bottom: solid 1px #eee;
		}
	}

	.el-dialog__body {
		padding: 30px 20px;
		color: #606266;
		font-size: 14px;
		word-break: break-all;
	}
}
</style>
