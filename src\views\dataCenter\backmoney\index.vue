<!--
 * @Author: x<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-16 14:03:21
 * @Description: 中医-数据中台-数据统计-退卡结算
 * @type: page
-->

<template>
	<el-card class="page-container">
		<Form class="demo-form-inline" ref="searchForm" :inline="true" :model="searchForm" :items="searchFormItems"
			:buttons="operationBtns" @search="handleSearch"/>

		<Table class="g-table-container" :tableData="table.tableList" :columns="table.columns" :total="table.total"
			:pagination="table.pagination" :loading="table.isTableLoading" :sizeChange="handleSizeChange"
			:currentChange="handleCurrentChange">
			<el-table-column slot="userRefundApprovalStatus" prop="userRefundApprovalStatus" label="审核状态">
				<template slot-scope="scope">
					<div class="tagStyle">
						<el-tag :type="Number(scope.row.userRefundApprovalStatus) === 2 ? 'success' : 'warning'">{{
							Number(scope.row.userRefundApprovalStatus) === 2 ? '已审核' :
								'待审核'
						}}</el-tag>
					</div>
				</template>
			</el-table-column>

			<el-table-column slot="status" prop="status" label="总公司结算状态">
				<template slot-scope="scope">
					<div class="tagStyle">
						<el-tag :type="Number(scope.row.status) === 5 ? 'success' : 'warning'">{{
							Number(scope.row.status) === 5 ? '已结算' :
								'待结算'
						}}</el-tag>
					</div>
				</template>
			</el-table-column>
			<el-table-column slot="companyRefundStatus" prop="companyRefundStatus" label="分公司结算状态">
				<template slot-scope="scope">
					<div class="tagStyle">
						<el-tag :type="Number(scope.row.
							companyRefundStatus
						) === 2 ? 'success' : 'warning'">{{ Number(scope.row.companyRefundStatus) === 2 ? '已结算' :
							'待结算'
						}}</el-tag>
					</div>
				</template>
			</el-table-column>
		</Table>
	</el-card>
</template>
<script>

import Form from "@/components/Combinecom/form";
import Table from "@/components/Combinecom/table";
import API from "@/api/dataCenter";
import { handleFormatMoney } from '@/utils/validate'

export default {
	name: "backmoney",
	components: { Form, Table },
	computed: {
		// 查询表单项
		searchFormItems() {
			return [
				{
					type: "text",
					size: "small",
					placeholder: "请输入分店名称或客户名称",
					label: "姓名",
					value: "searchName",
					limitLength: 20,
					clearable: true,
				},
			];
		},
		// 查询区按钮配置项
		operationBtns() {
			return [
				{
					class: "is-plain",
					type: "primary",
					size: "small",
					label: "搜索",
					icon: "el-icon-search",
					click: () => this.handleSearch()
				},
				{
					class: "is-plain",
					type: "info",
					size: "small",
					label: "重置",
					icon: "el-icon-refresh-left",
					click: () => this.handleReset(),
				},
			];
		},
	},
	data() {
		return {
			// 查询条件栏
			searchForm: {
				searchName: null,
				companyId: currentUser.company.id
				// companyId: "2647273530380673139"
			},
			// 表数据
			table: {
				// table参数
				total: 0,
				tableList: [],
				pagination: {
					limit: 20,
					offset: 0
				},
				columns: [
					// { type: "selection" },
					{
						label: "分店名称",
						prop: "company.name"
					},
					{
						label: "客户名称",
						prop: "name"
					},
					{
						label: "联系方式",
						prop: "phone"
					},
					{
						label: "卡号",
						prop: "card"
					},
					{
						label: "会员卡类型",
						prop: "type.name"
					},
					{
						label: "总公司结算总金额(元)",
						prop: "userRefundSum",
						formatter: row => {
							return handleFormatMoney(row.userRefundSum)
						}
					},
					{
						label: "结算时间",
						prop: "refundDate"
					},
					{
						label: "银行卡账户",
						prop: "account"

					},
					{
						slot: "userRefundApprovalStatus"
					},
					{
						slot: "status"
					},
					{
						slot: "companyRefundStatus"
					},

					{
						label: '操作',
						minWidth: 120,
						buttons: [
							{
								label: '审核',
								type: 'text',
								// disabled: true,
								click: row => this.handleCheck(row),
								hidden: row => Number(row.userRefundApprovalStatus) === 2
							},
							{
								label: '结算',
								type: 'text',
								// disabled: true,
								click: row => this.handleSend(row),
								hidden: row => Number(row.userRefundApprovalStatus) === 1 || Number(row.status) === 5
							},
							{
								label: '详情',
								type: 'text',
								click: row => this.handleCheckDetail(row),
							}
						]
					}
				],
				isTableLoading: false // 表格loading
			},
		};
	},
	mounted() {
		this.getTableData()
	},
	methods: {
		// 详情
		handleCheckDetail(row) {
			// console.log('跳转到详情页', row)
			this.$router.push({
				path: '/backmoneyDetail',
				query: { id: row.id }
			})
		},
		// total
		handleSizeChange(val) {
			// console.log('val', val)
			this.table.pagination.offset = val;
			this.getTableData();
		},
		// 改变页数
		handleCurrentChange(val) {
			// console.log('val2222', val)
			this.table.pagination.offset = val;
			this.getTableData();
		},
		// 查询
		handleSearch() {
			this.getTableData()
		},
		// 重置
		handleReset() {
			this.searchForm.searchName = null
			this.table.pagination.offset = 0

			this.getTableData()
		},
		// 审核
		handleCheck(row) {
			// console.log('审核row', row)
			this.$confirm('该操作不可撤销，确认审核选中的数据吗？', '确认', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				this.getCheckData(row)
			}).catch(() => {
				this.$message.info("已取消")
			})
		},
		// 结算
		handleSend(row) {
			// console.log('row', row)
			this.$confirm('该操作不可撤销，确认结算选中的数据吗？', '确认', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				this.getSendData(row)
			}).catch(() => {
				this.$message.info("已取消")
			})
		},

		// 审核
		async getCheckData(row) {

			try {
				const res = await API.reqBackCheck(row.id);
				if (res.code === '100') {
					this.$message.success('审核成功!');
					this.getTableData()
				} else {
					this.$message.warning(res.msg);
				}
			} catch (error) {
				this.$message.error(error);
			}

		},

		// 结算API
		async getSendData(row) {

			try {
				const res = await API.reqBackConsumeData(row.id);
				if (res.code === '100') {
					this.$message.success('结算成功!');
					this.getTableData()
				} else {
					this.$message.warning(res.msg);
				}
			} catch (error) {
				this.$message.error(error);
			}

		},

		// table数据
		async getTableData() {
			const data = {
				...this.searchForm,
				limit: this.table.pagination.limit,
				offset: this.table.pagination.offset ? (this.table.pagination.offset - 1) * this.table.pagination.limit : 0,
			};
			// console.log('data', data)
			// return
			try {
				this.table.isTableLoading = true;
				const res = await API.reqBackList(data);
				if (res.code === '100') {
					// 如果当前页无数据且不是第一页，则返回第一页
					if (!res.data && this.table.pagination.offset > 0) {
						this.table.pagination.offset = 0;
						this.getTableData()
					}

					if (res.data) {
						this.table.tableList = res.data.rows || [];
						this.table.total = Number(res.data.total);
					}

				} else {
					this.$message.warning(res.msg);
				}
				this.table.isTableLoading = false;
			} catch (error) {
				this.table.isTableLoading = false;
				this.$message.error(error);
			}
		}
	},
};
</script>

<style lang="scss" scoped>
.tagStyle {
	text-align: center;
}
</style>