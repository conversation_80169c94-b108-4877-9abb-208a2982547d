<template>
	<div class="container" v-loading="loading">
		<el-table ref="table" v-bind="$attrs" :data="tableData" border :height="banAutoHeight ? null : 'auto'"
			align="center" tooltip-effect="dark" :row-class-name="tableRowClassName"
			:header-row-class-name="tableHeaderClassName" :header-cell-style="getRowClass" :cell-style="getCellClass"
			:selectable="selectable" @selection-change="selectedChange">
			<template v-for="column in columns">
				<template v-if="
					!(typeof column.hidden === 'function'
						? column.hidden()
						: column.hidden)
				">
					<slot v-if="column.slot" :name="column.slot" />
					<el-table-column align="center" :selectable="selectable" v-else-if="column.type" v-bind="column"
						:width="column.width || '45'" :key="column.type" />
					<el-table-column align="center" :selectable="selectable" v-else-if="column.columnType === 'slot'"
						v-bind="column" :key="column.type">
						<template v-slot:default="scope">
							<!-- <img
                :width="column.width || '75px'"
                :height="column.height || '86px'"
                v-if="column.contentType === 'img'"
                @click="
                  column.click
                    ? column.click(formatterUrl(scope.row[column.img]))
                    : ''
                "
                :src="formatterUrl(scope.row[column.img])"
              /> -->
							<img :width="column.width || '75px'" :height="column.height || '86px'" v-if="column.contentType === 'img'"
								@click="
									column.click
										? column.click(scope.row[column.img])
										: ''
									" :src="scope.row[column.img]" />
							<span v-else>内容有问题</span>
						</template>
					</el-table-column>
					<el-table-column align="center" :selectable="selectable" v-else-if="column.buttons" v-bind="column"
						:label="column.label" :key="column.value">
						<template v-slot="{ row }">
							<el-button v-for="button in column.buttons.filter((button) =>
								typeof button.hidden === 'function'
									? !button.hidden(row)
									: !button.hidden
							)" :key="button.value" :class="typeof button.class === 'function'
								? button.class(row)
								: button.class
								" :loading="button.loading" :type="button.type" :size="button.size" :icon="button.icon" :disabled="typeof button.disabled === 'function'
									? button.disabled(row)
									: button.disabled
									" @click="button.click(row)">
								{{
									typeof button.label === "function"
										? button.label(row)
										: button.label
								}}
							</el-button>
						</template>
					</el-table-column>
					<el-table-column align="center" :selectable="selectable" v-else-if="
						!(typeof column.hidden === 'function'
							? column.hidden()
							: column.hidden)
					" :key="column.value" v-bind="column" :label="column.label" :formatter="column.formatter"
						:show-overflow-tooltip="showTip" />
				</template>
			</template>
		</el-table>
		<el-pagination v-if="pagination" background style="margin: 10px; text-align: right" :page-sizes="pageSizes"
			:page-size="pagination.pageSize || pagination.limit" :page-count="pageCount"
			:current-page="pagination.pageNum || pagination.offset" :total="Number(total)" :layout="layout"
			@current-change="currentPageChange" @size-change="pageSizeChange" />
	</div>
</template>

<script>
// import { formatterUrl } from "@/utils";
export default {
	name: "Table",
	inheritAttrs: false,
	props: {
		loading: {
			type: Boolean,
			default: false,
		},
		tableData: {
			type: Array,
			required: true,
		},
		selectedRow: {
			type: String,
		},
		columns: {
			type: Array,
			required: true,
		},
		showTip: {
			type: Boolean,
			default: true,
		},
		selectedChange: {
			type: Function,
			default: () => { },
		},
		// //拓展 部分不可勾选 默认返回 true 为可选
		selectable: {
			type: Function,
			default: () => {
				return true;
			},
		},
		singleSelect: {
			type: Boolean,
			default: false,
		},
		pagination: {
			type: Object,
			default: null,
		},
		pageCount: {
			type: Number,
			default: 5,
		},
		pageSizes: {
			type: Array,
			default: () => [20, 50, 100, Number(this.total || 0)],
		},
		total: {
			type: Number,
			default: 0,
		},
		layout: {
			type: String,
			default: "total, sizes, prev, pager, next, jumper",
		},
		currentChange: {
			type: Function,
			default: () => { },
		},
		sizeChange: {
			type: Function,
			default: () => { },
		},
		banAutoHeight: {
			type: Boolean,
			default: false,
		},
	},
	data() {
		return {};
	},
	computed: {
		defaultSelectRow() {
			return this.selectedRow;
		},
	},
	methods: {
		// formatterUrl(val) {
		//   return formatterUrl(val);
		// },
		currentPageChange(val) {
			// console.log('xiangfei', val)
			this.currentChange(val);
			this.calibrateTbodyScroll();
		},
		pageSizeChange(val) {
			this.sizeChange(val);
			this.calibrateTbodyScroll();
		},
		calibrateTbodyScroll() {
			let fatherElement = this.$refs.table.$el;
			let tbody = fatherElement.childNodes[2];
			tbody.scroll(0, 0);
		},
		tableRowClassName({ row, rowIndex }) {
			return "";
		},
		tableHeaderClassName({ row, rowIndex }) {
			return "";
		},
		getRowClass({ row, column, rowIndex, columnIndex }) {
			if (rowIndex === 0) {
				// return "border-bottom:1px solid #DBDCE2;padding:7px 5px;background:#F0F3F6;font-size:14px;color:#737981;";
				return "font-size:12px;color:#111111;text-align:center;"
			} else {
				return "padding:7px 5px;";
			}
		},
		getCellClass({ row, column, rowIndex, columnIndex }) {
			return "padding:7px 0;";
		},
		singleChoose(rows) {
			this.$refs.table.clearSelection();
			this.$refs.table.toggleRowSelection(rows[rows.length - 1]);
		},
	},
	watch: {
		defaultSelectRow(val) {
			const [key, value] = val.split("-");
			if (value) {
				for (let i = 0; i < this.tableData.length; i++) {
					const row = this.tableData[i];
					if (+value === row[key]) {
						// 设置默认选中行
						this.$nextTick(() => {
							this.$refs.table.toggleRowSelection(row, true);
						});
						break;
					}
				}
			}
		},
	},
};
</script>
<style>
.el-table__body-wrapper {
	height: calc(100% - 65px) !important;
}
</style>
