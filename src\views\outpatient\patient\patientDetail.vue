<!--
 * @Author: xiang<PERSON><PERSON>
 * @Date: 2025-06-11 10:51:11
 * @Description: 中医馆-客户档案-客户就诊记录-客户详情
 * @type: page
-->

<template>
	<el-dialog :title="dialogProps.title" :visible.sync="dialogProps.visible" :close-on-click-modal="false" width="80%"
		append-to-body>
		<div>
			<el-card class="box-card" shadow="always">
				<el-scrollbar view-style="height:calc(100vh - 210px);padding-right: 20px;">

					<!-- 基本信息 -->
					<el-card class="box-card main-card">
						<div slot="header" style="padding: 5px 0px; font-size: 14px; font-weight: bold">
							<span>基础信息</span>
						</div>
						<el-form :model="BasicInfoModel" ref="BasicInfoForm" label-width="auto">
							<el-row :gutter="24">
								<el-col :span="4">
									<el-form-item label="患者姓名:" prop="name">
										<div class="nameStyle">
											{{ BasicInfoModel.name ? BasicInfoModel.name : "" }}
											<div class="imgStyle" v-if="member.length > 0">
												<el-popover placement="top-start" title="" width="200" trigger="hover">
													<div v-for="(item, index) in member" :key="index" style="padding: 5px">
														<el-tag>{{ item.memberName }}</el-tag>
													</div>
													<img slot="reference" src="../../../assets/images/vip.png"
														style="width: 25px; height: 25px" />
												</el-popover>
											</div>
										</div>
										<div class="nameStyle" style="color: #ff0000" v-if="poverty.length > 0">
											[贫]
										</div>
									</el-form-item>
								</el-col>
								<el-col :span="3">
									<el-form-item label="性别:" prop="gender">
										<span>
											{{
												BasicInfoModel.gender.name
													? BasicInfoModel.gender.name
													: ""
											}}
										</span>
									</el-form-item>
								</el-col>
								<el-col :span="5">
									<el-form-item label="年龄:" prop="name">
										<span>
											{{ BasicInfoModel.age ? BasicInfoModel.age : 0 }}岁{{
												BasicInfoModel.month ? BasicInfoModel.month : 0
											}}月
										</span>
									</el-form-item>
								</el-col>
								<el-col :span="5">
									<el-form-item label="联系方式:" prop="phone">
										<span>
											{{ BasicInfoModel.phone ? BasicInfoModel.phone : "" }}
										</span>
									</el-form-item>
								</el-col>
								<el-col :span="5">
									<el-form-item label="身份证号:" prop="card">
										<span>
											{{ BasicInfoModel.card ? BasicInfoModel.card : "" }}
										</span>
									</el-form-item>
								</el-col>
							</el-row>

							<el-row :gutter="24">
								<el-col :span="4">
									<el-form-item label="治疗类型:">
										{{ registration.treatType.name || "" }}
									</el-form-item>
								</el-col>
								<el-col :span="3">
									<el-form-item label="传染病:" prop="infectType">
										{{ registration.infectType.name || "" }}
									</el-form-item>
								</el-col>
								<el-col :span="5">
									<el-form-item label="发病时间:" style="margin-right: 3px">
										{{ registration.morbidityTime }}
									</el-form-item>
								</el-col>
							</el-row>
						</el-form>
					</el-card>

					<!-- 病历信息 -->
					<el-card class="box-card main-card">
						<div slot="header" style="padding: 5px 0px; font-size: 14px; font-weight: bold">
							<span>病历</span>
						</div>
						<!-- 已就诊禁用选项 -->
						<el-form :model="MedicalRecordModel" ref="BasicInfoForm" label-width="auto" label-suffix="：">
							<el-row :gutter="24">
								<el-col :span="24">
									<el-form-item label="主诉" prop="patientTell">
										{{ MedicalRecordModel.patientTell }}
									</el-form-item>
								</el-col>
							</el-row>
							<el-row :gutter="24">
								<el-col :span="12">
									<el-form-item label="体温" prop="" width="90">
										<div class="ipt-box">
											{{ `${BasicInfoModel.temperature ? BasicInfoModel.temperature + '℃' : ''}` }}
										</div>
									</el-form-item>
								</el-col>
								<el-col :span="12">
									<el-form-item label="脉搏" prop="">
										<div class="ipt-box">
											{{ `${BasicInfoModel.pulse ? BasicInfoModel.pulse + '次/min' : ''}` }}
										</div>
									</el-form-item>
								</el-col>
								<el-col :span="12">
									<el-form-item label="呼吸" prop="">
										<div class="ipt-box">
											{{ `${BasicInfoModel.breathe ? BasicInfoModel.breathe + '次/min' : ''}` }}
										</div>
									</el-form-item>
								</el-col>
								<el-col :span="12">
									<el-form-item label="血压" prop="">
										<div class="ipt-box">
											{{ `${BasicInfoModel.bloodPressure ? BasicInfoModel.bloodPressure + 'mmHg' : ''}` }}
										</div>
									</el-form-item>
								</el-col>
							</el-row>
							<el-row :gutter="24">
								<el-col :span="24">
									<el-form-item label="既往史" prop="beforeHistory">
										{{ MedicalRecordModel.beforeHistory }}
									</el-form-item>
								</el-col>
								<el-col :span="24">
									<el-form-item label="现病史" prop="nowHistory">
										{{ MedicalRecordModel.nowHistory }}
									</el-form-item>
								</el-col>
								<el-col :span="24">
									<el-form-item label="刻下证" prop="engravedCertificate">
										{{ MedicalRecordModel.engravedCertificate }}
									</el-form-item>
								</el-col>
								<el-col :span="24">
									<el-form-item label="中医四诊" prop="cnFourDiagnose">
										{{ MedicalRecordModel.cnFourDiagnose }}
									</el-form-item>
								</el-col>
							</el-row>
							<el-form-item label="西医诊断" prop="westernDiagnose">
								{{ MedicalRecordModel.westernDiagnose }}
							</el-form-item>
							<el-form-item label="中医诊断" prop="chinaDiagnose">
								{{ MedicalRecordModel.chinaDiagnose }}
							</el-form-item>
							<el-form-item label="中医证候" prop="chinaSyndrome">
								{{ MedicalRecordModel.chinaSyndrome }}
							</el-form-item>
							<el-row :gutter="24">
								<el-col :span="12">
									<el-form-item label="是否就诊主要原因" prop="b02C">
										{{ MedicalRecordModel.b02C === '1' ? '是' : '否' }}
									</el-form-item>
								</el-col>

								<el-col :span="12">
									<el-form-item label="是否留观" prop="c11C">
										{{ MedicalRecordModel.c11C === '1' ? '是' : '否' }}
									</el-form-item>
								</el-col>

								<el-col :span="12">
									<el-form-item label="是否抢救" prop="c12C">
										{{ MedicalRecordModel.c12C === '1' ? '是' : '否' }}
									</el-form-item>
								</el-col>

								<el-col :span="12">
									<el-form-item label="患者去向" prop="patientDirection">
										{{ MedicalRecordModel.patientDirection ? MedicalRecordModel.patientDirection.name : "" }}
									</el-form-item>
								</el-col>
							</el-row>
							<!-- 附件上传 -->
							<el-row :gutter="24">
								<el-col :span="24">
									<upload-file :objectId="MedicalFileObjId" ref="medicalFile" @getFileList="getFileList"
										:action="MedicalFlags"></upload-file>
								</el-col>
							</el-row>
						</el-form>
					</el-card>

					<!-- 开具处方 -->
					<el-card class="box-card main-card">
						<div slot="header" style="padding: 5px 0px; font-size: 14px; font-weight: bold">
							<span>开具处方</span>
						</div>
						<div style="min-height: 400px">
							<el-tabs v-model="medicalEditTabsValue" type="card" @tab-remove="removeMedicalEditTab"
								@tab-click="clickMedicalEditTab">
								<el-tab-pane :closable="false" v-if="!isReadOnly" disabled key="add" name="add">
									<span slot="label">
										<el-popover placement="bottom-start" popper-class="medical-type-popover" trigger="click">
											<el-button type="text" slot="reference"><i class="el-icon-plus el-icon--right"></i>开处方</el-button>
											<ul class="medical-type-ul">
												<li v-for="(item, index) in medicalTypeList" :key="item">
													<el-button @click="addMedicalEditTab(item)" plain size="mini" style="border: none">{{
														item.name
													}}</el-button>
												</li>
											</ul>
										</el-popover>
									</span>
								</el-tab-pane>
								<el-tab-pane v-for="(item, index) in medicalEditTabs" :key="item" :label="item.title" :name="item"
									:closable="item.closable">
									<span slot="label">
										{{ item.title }}
									</span>
									<!-- 西药处方 -->
									<div v-if="
										item.type === 'recipelType_0' ||
										item.type === 'recipelType_5'
									" style="margin-top: 10px">
										<el-row>
											<el-divider content-position="left">处方信息</el-divider>
											<div v-if="
												item.type === 'recipelType_0' ||
												item.type === 'recipelType_2'
											">
												<el-select :disabled="isReadOnly ||
													item.content.recipelInfo.chargeStatus != 0 ||
													item.content.recipelInfo.status == -1
													" v-model="item.content.recipelInfo.smallType" placeholder="请选择处方分类" style="width: 100px">
													<el-option v-for="smallTypeItem in RecipelSmallTypeList" :key="smallTypeItem.value"
														:label="smallTypeItem.name" :value="{
															name: smallTypeItem.name,
															value: smallTypeItem.value,
														}">
													</el-option>
												</el-select>
											</div>
											<el-popover placement="top-start" v-if="
												!isReadOnly &&
												item.content.recipelInfo.chargeStatus == 0 &&
												item.content.recipelInfo.status != -1
											" width="700" trigger="focus">
												<el-table :data="WesternMedicineTable" :height="300" border highlight-current-row
													@row-click="RowClickWesternTable">
													<el-table-column prop="drug.type.name" label="药品类型">
													</el-table-column>
													<el-table-column prop="drug.goodsName" label="药品名称">
													</el-table-column>
													<el-table-column prop="gg" label="规格" width="120">
														<template slot-scope="scope">
															{{ scope.row.drug.dosis
															}}{{ scope.row.drug.dosisUnit.name }} *
															{{ scope.row.drug.preparation
															}}{{ scope.row.drug.preparationUnit.name }}/{{
																scope.row.drug.pack.name
															}}
														</template>
													</el-table-column>
													<el-table-column prop="drug.factory.name" label="厂家" width="100">
													</el-table-column>
													<el-table-column label="销售价" width="80">
														<template slot-scope="scope">
															{{
																scope.row.drug.price.toFixed(2) +
																"/" +
																scope.row.drug.pack.name
															}}
														</template>
													</el-table-column>
													<el-table-column label="拆零价" width="80">
														<template slot-scope="scope">
															{{
																scope.row.drug.isUnpackSell == 1
																	? scope.row.drug.retailPrice.toFixed(4) +
																	"/" +
																	scope.row.drug.preparationUnit.name
																	: "--"
															}}
														</template>
													</el-table-column>
													<el-table-column v-if="!item.content.recipelInfo.isPre" prop="surplusStock" label="可用库存"
														width="100">
														<template slot-scope="scope">
															{{
																Math.floor(
																	scope.row.surplusStock /
																	scope.row.drug.preparation
																) > 0
																	? Math.floor(
																		scope.row.surplusStock /
																		scope.row.drug.preparation
																	) +
																	scope.row.drug.pack.name +
																	(scope.row.surplusStock %
																		scope.row.drug.preparation >
																		0
																		? (scope.row.surplusStock %
																			scope.row.drug.preparation) +
																		scope.row.drug.preparationUnit.name
																		: "")
																	: scope.row.surplusStock +
																	scope.row.drug.preparationUnit.name
															}}
														</template>
													</el-table-column>
												</el-table>
												<el-input prefix-icon="el-icon-plus" suffix-icon="el-icon-search" style="width: 30%"
													slot="reference" ref="WesternInput" v-model="SearchWesternInput" @input="GetWesternTable"
													@focus="
														GetWesternTable(item.content.recipelInfo.isPre)
														" placeholder="输入药品名称或拼音码"></el-input>
											</el-popover>

											<el-checkbox style="margin-left: 20px" v-model="item.content.recipelInfo.chronicDisease"
												:disabled="isReadOnly ||
													item.content.recipelInfo.chargeStatus != 0 ||
													item.content.recipelInfo.status == -1
													">
												是否慢病{{
													item.content.recipelInfo.chronicDisease
														? "(慢病处方最多可开" +
														systemParamConfig.chronicDays +
														"天)"
														: "(非慢病处方最多可开" +
														systemParamConfig.normalDays +
														"天)"
												}}
											</el-checkbox>
											<el-checkbox style="margin-left: 20px" v-model="item.content.recipelInfo.isPre" :disabled="isReadOnly ||
												item.content.recipelInfo.chargeStatus != 0 ||
												item.content.recipelInfo.status == -1
												" @change="DeleteMedicalRow('', '', item)">
												是否电子处方
											</el-checkbox>
											<el-button type="primary" v-if="!isReadOnly" style="float: right" plain
												@click="historyRecipel(item.type, index)">历史处方
											</el-button>
											<span style="float: right">&nbsp;&nbsp;&nbsp;</span>
											<el-button type="primary" v-if="!isReadOnly" style="float: right; margin-left: 120px" plain
												@click="templateRecipel(item.type, index)">模板处方
											</el-button>
											<el-button type="primary" v-if="!isReadOnly" style="float: right; margin-right: -105px" plain
												@click="saveTemplate(item.type, index)">存为模板
											</el-button>
										</el-row>
										<!-- 处方详细-->
										<el-row>
											<el-table :data="getDataFilterTable(
												item.content.recipelDetailEvtList,
												0,
												item
											)
												" border style="width: 100%" class="tableStyle" @cell-click="checkInventory">
												<el-table-column type="index" label="序号" align="center">
												</el-table-column>
												<el-table-column prop="drugStuffId" label="药品名称" align="center">
													<template slot-scope="scope">
														{{ scope.row.drugStuffId.name }}
														<span style="color: forestgreen">
															{{
																scope.row.drugStuffId.drug.dosis +
																scope.row.drugStuffId.drug.dosisUnit.name +
																"*" +
																scope.row.drugStuffId.drug.preparation +
																scope.row.drugStuffId.drug.preparationUnit
																	.name +
																"/" +
																scope.row.drugStuffId.drug.pack.name
															}}
														</span>
													</template>
												</el-table-column>
												<el-table-column prop="singleDosage" label="单次用量" align="center">
													<template slot-scope="scope">
														<el-input v-model="scope.row.singleDosage" ref="westernMedicine"
															oninput="value=value.replace(/[^\d.]/g,'')" @input="MedicalCalculate()" :disabled="isReadOnly ||
																item.content.recipelInfo.chargeStatus != 0 ||
																item.content.recipelInfo.status == -1
																">
															<template slot="append">{{ scope.row.drugStuffId.dosisUnit.name }}
															</template>
														</el-input>
													</template>
												</el-table-column>
												<el-table-column prop="westernMedicineUse" label="用法" align="center">
													<template slot-scope="scope">
														<el-select v-model="scope.row.westernMedicineUse" :disabled="isReadOnly ||
															item.content.recipelInfo.chargeStatus != 0 ||
															item.content.recipelInfo.status == -1
															" placeholder="" @change="changeUse(scope.row.westernMedicineUse)">
															<el-option v-for="item in WesternUseOption" :key="item.value" :label="item.name"
																:value="{ name: item.name, value: item.value }">
															</el-option>
														</el-select>
													</template>
												</el-table-column>
												<el-table-column prop="frequency" label="频次" align="center">
													<template slot-scope="scope">
														<el-select v-model="scope.row.frequency" :disabled="isReadOnly ||
															item.content.recipelInfo.chargeStatus != 0 ||
															item.content.recipelInfo.status == -1
															" @change="MedicalCalculate()" placeholder="">
															<el-option v-for="item in FrequencyOption" :key="item.value" :label="item.name" :value="{
																name: item.name,
																value: item.value,
															}">
															</el-option>
														</el-select>
													</template>
												</el-table-column>
												<el-table-column prop="days" label="天数" align="center">
													<template slot-scope="scope">
														<el-input-number v-model="scope.row.days.name" :min="0" :max="item.content.recipelInfo.chronicDisease
															? systemParamConfig.chronicDays
															: systemParamConfig.normalDays
															" :disabled="isReadOnly ||
																item.content.recipelInfo.chargeStatus != 0 ||
																item.content.recipelInfo.status == -1
																" :controls="false" @change="onChronicDiseaseChange">
														</el-input-number>
													</template>
												</el-table-column>
												<el-table-column v-if="item.content.recipelInfo.isPre" prop="total" label="总量" align="center">
													<template slot-scope="scope">
														<el-input-number v-model="scope.row.total" :min="0" :disabled="isReadOnly ||
															item.content.recipelInfo.chargeStatus != 0 ||
															item.content.recipelInfo.status == -1
															" :controls="false">
														</el-input-number>
													</template>
												</el-table-column>
												<el-table-column v-if="!item.content.recipelInfo.isPre" prop="total" label="总量" align="center">
													<template slot-scope="scope">
														{{
															Math.floor(
																scope.row.total /
																scope.row.drugStuffId.drug.preparation
															) > 0
																? Math.floor(
																	scope.row.total /
																	scope.row.drugStuffId.drug.preparation
																) +
																scope.row.drugStuffId.pack.name +
																(scope.row.total %
																	scope.row.drugStuffId.drug.preparation >
																	0
																	? (scope.row.total %
																		scope.row.drugStuffId.drug
																			.preparation) +
																	scope.row.drugStuffId.preparationUnit.name
																	: "")
																: scope.row.total +
																scope.row.drugStuffId.preparationUnit.name
														}}
													</template>
												</el-table-column>
												<el-table-column v-if="!item.content.recipelInfo.isPre" prop="isUnpackSell" label="单价"
													align="center">
													<template slot-scope="scope">
														<el-select v-model="scope.row.isUnpackSell" :disabled="isReadOnly ||
															item.content.recipelInfo.chargeStatus != 0 ||
															item.content.recipelInfo.status == -1
															" placeholder="请选择" @change="MedicalCalculate()" style="width: 110px">
															<el-option :label="scope.row.drugStuffId.price.toFixed(4) +
																'/' +
																scope.row.drugStuffId.pack.name
																" :value="0"></el-option>
															<el-option :label="scope.row.drugStuffId.retailPrice +
																'/' +
																scope.row.drugStuffId.preparationUnit.name
																" :value="1"></el-option>
														</el-select>
													</template>
												</el-table-column>

												<el-table-column v-if="!item.content.recipelInfo.isPre" prop="allFee" label="金额" align="center">
												</el-table-column>
												<el-table-column v-if="
													!isReadOnly &&
													item.content.recipelInfo.chargeStatus == 0 &&
													item.content.recipelInfo.status != -1
												" label="操作" fixed="right" align="center" width="80">
													<template slot-scope="scope">
														<i class="el-icon-circle-close" @click="DeleteMedicalRow(scope.$index, scope.row)"></i>
														<span style="display: none">{{ changeData }}</span>
													</template>
												</el-table-column>
											</el-table>
										</el-row>
									</div>
									<!-- 中药处方 -->
									<div v-if="item.type === 'recipelType_1'" style="margin-top: 15px">
										<el-row>
											<el-divider content-position="left">处方信息</el-divider>

											<el-popover placement="top-start" v-if="
												!isReadOnly &&
												item.content.recipelInfo.chargeStatus == 0 &&
												item.content.recipelInfo.status != -1
											" width="700" trigger="focus">
												<el-table :data="ChineseMedicineTable" :height="300" border highlight-current-row
													@row-click="RowClickChineseTable">
													<el-table-column prop="drug.type.name" label="药品类型">
													</el-table-column>
													<el-table-column prop="drug.goodsName" label="药品名称">
													</el-table-column>
													<el-table-column prop="gg" label="规格" width="120">
														<template slot-scope="scope">
															{{ scope.row.drug.dosis
															}}{{ scope.row.drug.dosisUnit.name }} *
															{{ scope.row.drug.preparation
															}}{{ scope.row.drug.preparationUnit.name }}/{{
																scope.row.drug.pack.name
															}}
														</template>
													</el-table-column>
													<el-table-column prop="drug.factory.name" label="厂家" width="100">
													</el-table-column>
													<el-table-column label="销售价" width="80">
														<template slot-scope="scope">
															{{
																scope.row.drug.price.toFixed(4) +
																"/" +
																scope.row.drug.pack.name
															}}
														</template>
													</el-table-column>
													<el-table-column prop="surplusStock" label="库存" width="100">
														<template slot-scope="scope">
															{{
																Math.floor(
																	scope.row.surplusStock /
																	scope.row.drug.preparation
																) > 0
																	? Math.floor(
																		scope.row.surplusStock /
																		scope.row.drug.preparation
																	) +
																	scope.row.drug.pack.name +
																	(scope.row.surplusStock %
																		scope.row.drug.preparation >
																		0
																		? (scope.row.surplusStock %
																			scope.row.drug.preparation) +
																		scope.row.drug.preparationUnit.name
																		: "")
																	: scope.row.surplusStock +
																	scope.row.drug.preparationUnit.name
															}}
														</template>
													</el-table-column>
												</el-table>
												<el-input prefix-icon="el-icon-plus" suffix-icon="el-icon-search" style="width: 30%"
													slot="reference" id="WesternInput" v-model="SearchChineseInput" @input="
														GetChineseTable(item.content.recipelInfo.isPre)
														" @focus="
															GetChineseTable(item.content.recipelInfo.isPre)
															" @keydown.enter.native="focusNextInput('2')" placeholder="输入药品名称或拼音码"></el-input>
											</el-popover>
											<el-button type="primary" v-if="!isReadOnly" style="float: right" plain
												@click="historyRecipel(item.type, index)">历史处方
											</el-button>
											<span style="float: right">&nbsp;&nbsp;&nbsp;</span>
											<el-button type="primary" v-if="!isReadOnly" style="float: right; margin-left: 120px" plain
												@click="templateRecipel(item.type, index)">模板处方
											</el-button>
											<el-button type="primary" v-if="!isReadOnly" style="float: right; margin-right: -105px" plain
												@click="saveTemplate(item.type, index)">存为模板
											</el-button>
										</el-row>
										<el-row :gutter="24" style="min-height: 100px; display: flex; flex-wrap: wrap">
											<el-col :span="8" v-for="(citem, index) in getDataFilterTable(
												item.content.recipelDetailEvtList,
												0,
												item
											)" :key="citem" :offset="0">
												<!-- 处方卡片 -->
												<el-card :index="index" class="box-card chinese-medicine-card"
													style="width: 220px; margin-bottom: 10px">
													<div slot="header" class="clearfix" style="padding: 5px 0px; font-size: 14px">
														<span>{{ citem.drugStuffId.name }}</span>
														<el-button v-if="
															!isReadOnly &&
															item.content.recipelInfo.chargeStatus == 0 &&
															item.content.recipelInfo.status != -1
														" :disabled="isReadOnly ||
															item.content.recipelInfo.chargeStatus != 0 ||
															item.content.recipelInfo.status == -1
															" style="float: right; padding: 3px 0" type="text">
														</el-button>
													</div>
													<div>
														<el-input :disabled="isReadOnly ||
															item.content.recipelInfo.chargeStatus != 0 ||
															item.content.recipelInfo.status == -1
															" placeholder="数量" size="mini" ref="herbMedicine" v-model="citem.singleDosage" style="width: 80px">
															<template slot="append">{{ citem.drugStuffId.pack.name }}
															</template>
														</el-input>
														<el-select :disabled="isReadOnly ||
															item.content.recipelInfo.chargeStatus != 0 ||
															item.content.recipelInfo.status == -1
															" size="mini" v-model="citem.chineseMedicineUse" placeholder="" style="width: 105px">
															<el-option v-for="oitem in ChineseUseTimeOption" :key="oitem.value" :label="oitem.name"
																:value="{
																	name: oitem.name,
																	value: oitem.value,
																}">
															</el-option>
														</el-select>
													</div>
												</el-card>
											</el-col>
										</el-row>
										<el-row>
											<el-input :disabled="isReadOnly ||
												item.content.recipelInfo.chargeStatus != 0 ||
												item.content.recipelInfo.status == -1
												" v-model="item.content.recipelInfo.dosage" oninput="value=value.replace(/[^\d.]/g,'')"
												@input="MedicalCalculate()" id="dosageInput" style="width: 60px"></el-input>

											&nbsp;剂 &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;用法：
											<el-select :disabled="isReadOnly ||
												item.content.recipelInfo.chargeStatus != 0 ||
												item.content.recipelInfo.status == -1
												" v-model="item.content.recipelInfo.recipelUse" placeholder="请选择" style="width: 110px" @change="exchage">
												<el-option v-for="pitem in ChineseUseOption" :key="pitem.value" :label="pitem.name"
													:value="{ name: pitem.name, value: pitem.value }">
												</el-option>
											</el-select>
											<el-select :disabled="isReadOnly ||
												item.content.recipelInfo.chargeStatus != 0 ||
												item.content.recipelInfo.status == -1
												" v-model="item.content.recipelInfo.frequency" placeholder="请选择" style="width: 110px" @change="exchage">
												<el-option v-for="pitem in ChineseTimeOption" :key="pitem.value" :label="pitem.name"
													:value="{ name: pitem.name, value: pitem.value }">
												</el-option>
											</el-select>
											<el-select :disabled="isReadOnly ||
												item.content.recipelInfo.chargeStatus != 0 ||
												item.content.recipelInfo.status == -1
												" v-model="item.content.recipelInfo.takeFrequency" placeholder="请选择" style="width: 110px"
												@change="exchage">
												<el-option v-for="pitem in FrequencyOption" :key="pitem.value" :label="pitem.name" :value="{
													name: pitem.name,
													value: pitem.value,
												}">
												</el-option>
											</el-select>

											&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; 一次&nbsp;
											<el-input id="singleDosageInput" :disabled="isReadOnly ||
												item.content.recipelInfo.chargeStatus != 0 ||
												item.content.recipelInfo.status == -1
												" @input="singleDosageHandler" v-model="item.content.recipelInfo.singleDosage" style="width: 60px">
											</el-input>

											&nbsp; <span v-if="isSpecial">ml</span><span v-else>ml</span>
											<span style="float: right">中药金额:{{
												item.content.recipelInfo.medicalAmount
													? item.content.recipelInfo.medicalAmount
													: 0
											}}元</span>
											<el-input :disabled="isReadOnly ||
												item.content.recipelInfo.chargeStatus != 0 ||
												item.content.recipelInfo.status == -1
												" v-model="item.content.recipelInfo.chinessNotes" placeholder="输入备注选项" style="margin-top: 10px"></el-input>
										</el-row>
									</div>
									<!-- 诊疗项目 -->
									<div v-if="item.type === 'recipelType_3'" style="margin-top: 15px">
										<el-row>
											<el-divider content-position="left">处方信息</el-divider>
											<el-popover placement="top-start" v-if="
												!isReadOnly &&
												item.content.recipelInfo.chargeStatus == 0 &&
												item.content.recipelInfo.status != -1
											" width="540" trigger="focus">
												<el-table :data="TreatmentTable" :height="300" border highlight-current-row
													@row-click="RowClickTreatmentTable">
													<el-table-column prop="itemName" label="项目名称" width="200">
													</el-table-column>
													<el-table-column prop="itemType.name" label="项目类型" width="100">
													</el-table-column>
													<el-table-column prop="isPackage" label="套餐项目" width="100">
														<template slot-scope="scope">
															{{ scope.row.isPackage === "1" ? "是" : "否" }}
														</template>
													</el-table-column>
													<el-table-column prop="salePrice" label="单价" width="120">
														<template slot-scope="scope">
															{{ scope.row.salePrice.toFixed(4) }}/{{
																scope.row.unit.name
															}}
														</template>
													</el-table-column>
												</el-table>
												<el-input prefix-icon="el-icon-plus" suffix-icon="el-icon-search" style="width: 30%"
													slot="reference" ref="TreatmentInput" v-model="SearchCostItemInput" @input="GetCostItemTable"
													@focus="GetCostItemTable" placeholder="输入诊疗项目名称或拼音码"></el-input>
											</el-popover>
											<el-button type="primary" v-if="!isReadOnly" style="float: right" plain
												@click="historyRecipel(item.type, index)">历史处方
											</el-button>
											<span style="float: right">&nbsp;&nbsp;&nbsp;</span>
											<el-button type="primary" v-if="!isReadOnly" style="float: right; margin-left: 120px" plain
												@click="templateRecipel(item.type, index)">模板处方
											</el-button>
											<!-- <span style="float:right;">&nbsp;&nbsp;&nbsp;</span> -->
											<el-button type="primary" v-if="!isReadOnly" style="float: right; margin-right: -105px" plain
												@click="saveTemplate(item.type, index)">存为模板
											</el-button>
										</el-row>
										<el-row>
											<el-table :data="getDataFilterTable(
												item.content.recipelDetailEvtList,
												0,
												item
											)
												" border style="width: 100%" class="tableStyle">
												<el-table-column type="index" label="序号" align="center">
												</el-table-column>
												<el-table-column prop="drugStuffId.name" label="项目名称" align="center">
												</el-table-column>
												<el-table-column prop="drugStuffId.costItem.itemType.name" label="项目类别"
													align="center"></el-table-column>
												<el-table-column prop="drugStuffId.costItem.isPackage" label="套餐项目" align="center">
													<template slot-scope="scope">
														{{
															scope.row.drugStuffId.costItem.isPackage === "1"
																? "是"
																: "否"
														}}
													</template>
												</el-table-column>
												<el-table-column prop="total" label="数量" align="center">
													<template slot-scope="scope">
														<el-input v-model="scope.row.total" ref="DiagnosisTreatment"
															oninput="value=value.replace(/[^\d.]/g,'')" @input="MedicalCalculate()" :disabled="isReadOnly ||
																item.content.recipelInfo.chargeStatus != 0 ||
																item.content.recipelInfo.status == -1
																">
															<template slot="append">{{ scope.row.drugStuffId.pack.name }}
															</template>
														</el-input>
													</template>
												</el-table-column>
												<el-table-column label="单价" align="center">
													<template slot-scope="scope">
														{{ scope.row.drugStuffId.price.toFixed(4) }}/{{
															scope.row.drugStuffId.pack.name
														}}
													</template>
												</el-table-column>
												<el-table-column prop="allFee" label="合计金额" align="center">
												</el-table-column>
												<el-table-column v-if="
													!isReadOnly &&
													item.content.recipelInfo.chargeStatus == 0 &&
													item.content.recipelInfo.status != -1
												" label="操作" fixed="right" align="center" width="50">
													<template slot-scope="scope">
														<i class="el-icon-circle-close" @click="DeleteMedicalRow(scope.$index, scope.row)"></i>
													</template>
												</el-table-column>
												<el-table-column v-else-if="inspectionSign == 1" label="操作" fixed="right" align="center"
													width="80">
													<template slot-scope="scope">
														<div v-if="inspectionType[scope.$index] == 1">
															<el-button @click="lookInspection(scope.row.drugStuffId)" type="text">查看
															</el-button>
														</div>
														<div v-else>
															<el-button type="text" disabled>还未填写</el-button>
														</div>
													</template>
												</el-table-column>
											</el-table>
										</el-row>
									</div>
									<div style="margin-top: 10px">
										<el-row>
											<el-divider content-position="left">附加费</el-divider>
											<el-popover placement="top-start" width="550" trigger="click" v-if="
												!isReadOnly &&
												item.content.recipelInfo.chargeStatus == 0 &&
												item.content.recipelInfo.status != -1
											">
												<!-- 附加费table SurchargeTable -->
												<el-table :data="SurchargeTable" :max-height="300" border highlight-current-row
													@row-click="RowClickSurchargeTable">
													<el-table-column label="费用名称">
														<template slot-scope="scope">
															{{
																scope.row.stuffType === "4"
																	? scope.row.name
																	: scope.row.itemName
															}}
														</template>
													</el-table-column>
													<el-table-column label="销售价" width="100">
														<template slot-scope="scope">
															{{
																scope.row.stuffType === "4"
																	? scope.row.priceOutSell + "/" + scope.row.packUnit.name
																	: scope.row.salePrice + "/" + scope.row.unit.name
															}}
														</template>
													</el-table-column>
													<el-table-column label="零售价" width="100">
														<template slot-scope="scope">
															{{
																scope.row.stuffType === "4" && scope.row.isUnpackSell == "1"
																	? scope.row.retailPrice + "/" + scope.row.minUnit.name
																	: "--"
															}}
														</template>
													</el-table-column>
													<el-table-column label="库存" width="100">
														<template slot-scope="scope">
															{{ scope.row.stuffType === "4" ? scope.row.stock.surplusStock : "不限制" }}
														</template>
													</el-table-column>
												</el-table>
												<el-input prefix-icon="el-icon-plus" suffix-icon="el-icon-search" id="fjinput"
													style="width: 30%" slot="reference" v-model="SearchSurchargeInput" @input="GetSurchargeTable"
													@focus="GetSurchargeTable" placeholder="输入附加费名称或拼音码">
												</el-input>
											</el-popover>
										</el-row>
										<!-- 附加费table -->
										<div v-if="item.type != 'recipelType_2'">
											<el-row>
												<el-table :data="getDataFilterTable(
													medicalClickTabsValue.content.recipelDetailEvtList,
													1,
													item.costDebug
												)
													" border style="width: 100%" class="tableStyle">
													<el-table-column type="index" label="序号" width="50" align="center">
													</el-table-column>
													<el-table-column prop="drugStuffId.name" label="费用名称" width="200"
														align="center"></el-table-column>
													<el-table-column prop="price" label="单价" width="100" align="center">
														<template slot-scope="scope">
															<span v-if="scope.row.stuffType === '3'">
																{{
																	scope.row.drugStuffId.price.toFixed(4) +
																	"/" +
																	scope.row.drugStuffId.pack.name
																}}
															</span>
															<span v-if="scope.row.stuffType === '4'">
																{{
																	scope.row.isUnpackSell == 0
																		? scope.row.drugStuffId.price.toFixed(4) +
																		"/" +
																		scope.row.drugStuffId.pack.name
																		: scope.row.drugStuffId.retailPrice +
																		"/" +
																		scope.row.drugStuffId.preparationUnit.name
																}}
															</span>
														</template>
													</el-table-column>
													<el-table-column label="数量" align="center" width="200">
														<template slot-scope="scope">
															<el-input :disabled="isReadOnly ||
																item.content.recipelInfo.chargeStatus != 0 ||
																item.content.recipelInfo.status == -1
																" ref="additionalCharge" v-model="scope.row.singleDosage" @input="MedicalCalculate()"
																style="width: 60px">
															</el-input>
															<el-select :disabled="isReadOnly ||
																item.content.recipelInfo.chargeStatus != 0 ||
																item.content.recipelInfo.status == -1
																" v-model="scope.row.isUnpackSell" @change="MedicalCalculate()" placeholder="请选择" style="width: 80px">
																<el-option :label="scope.row.drugStuffId.pack.name" :value="0"></el-option>
																<el-option v-if="scope.row.drugStuffId.isUnpackSell == 1" :label="scope.row.drugStuffId.preparationUnit.name
																	" :value="1"></el-option>
															</el-select>
														</template>
													</el-table-column>
													<el-table-column prop="allFee" label="金额" align="center">
													</el-table-column>
													<el-table-column v-if="
														!isReadOnly &&
														item.content.recipelInfo.chargeStatus == 0 &&
														item.content.recipelInfo.status != -1
													" label="操作" fixed="right" width="80" align="center">
														<template slot-scope="scope">
															<i class="el-icon-circle-close" @click="
																DeleteMedicalRow(scope.$index, scope.row)
																"></i>
														</template>
													</el-table-column>
												</el-table>
											</el-row>
										</div>
										<div v-else>
											<el-row>
												<el-table :data="getDataFilterTable(item.infusion.excharge, 1, item)
													" border style="width: 100%" class="tableStyle">
													<el-table-column type="index" label="序号" width="50" align="center">
													</el-table-column>
													<el-table-column prop="drugStuffId.name" label="费用名称" width="200"
														align="center"></el-table-column>

													<el-table-column prop="price" label="单价" width="120" align="center">
														<template slot-scope="scope">
															<span v-if="scope.row.stuffType === '3'">
																{{
																	scope.row.drugStuffId.price.toFixed(4) +
																	"/" +
																	scope.row.drugStuffId.pack.name
																}}
															</span>
															<span v-if="scope.row.stuffType === '4'">
																{{
																	scope.row.isUnpackSell == 0
																		? scope.row.drugStuffId.price.toFixed(4) +
																		"/" +
																		scope.row.drugStuffId.pack.name
																		: scope.row.drugStuffId.retailPrice +
																		"/" +
																		scope.row.drugStuffId.preparationUnit.name
																}}
															</span>
														</template>
													</el-table-column>
													<el-table-column label="数量" align="center" width="200">
														<template slot-scope="scope">
															<el-input :disabled="isReadOnly ||
																item.content.recipelInfo.chargeStatus != 0 ||
																item.content.recipelInfo.status == -1
																" ref="additionalCharge" v-model="scope.row.singleDosage" @input="MedicalCalculate()"
																style="width: 60px">
															</el-input>
															<el-select :disabled="isReadOnly ||
																item.content.recipelInfo.chargeStatus != 0 ||
																item.content.recipelInfo.status == -1
																" v-model="scope.row.isUnpackSell" @change="MedicalCalculate()" placeholder="请选择" style="width: 80px">
																<el-option :label="scope.row.drugStuffId.pack.name" :value="0"></el-option>
																<el-option v-if="scope.row.drugStuffId.isUnpackSell == 1" :label="scope.row.drugStuffId.preparationUnit.name
																	" :value="1"></el-option>
															</el-select>
														</template>
													</el-table-column>
													<el-table-column prop="allFee" label="金额" align="center">
													</el-table-column>
													<el-table-column v-if="
														!isReadOnly &&
														item.content.recipelInfo.chargeStatus == 0 &&
														item.content.recipelInfo.status != -1
													" label="操作" fixed="right" width="80" align="center">
														<template slot-scope="scope">
															<i class="el-icon-circle-close" @click="
																DeleteExMedicalRow(scope.$index, scope.row)
																"></i>
														</template>
													</el-table-column>
												</el-table>
											</el-row>
										</div>
										<el-row style="margin-top: 10px">
											<el-divider content-position="left">医嘱事项</el-divider>
											<el-input type="textarea" :rows="4" placeholder="请输入医嘱事项"
												v-model="item.content.recipelInfo.entrust" :disabled="isReadOnly ||
													item.content.recipelInfo.chargeStatus != 0 ||
													item.content.recipelInfo.status == -1
													">
											</el-input>
										</el-row>
									</div>
								</el-tab-pane>
							</el-tabs>
						</div>
					</el-card>
					<div style="height: 10px"></div>
				</el-scrollbar>
			</el-card>
			<div slot="footer" class='dialog-footer'>
				<el-button :plain='true' @click="dialogProps.visible = false">关 闭</el-button>
			</div>
		</div>
	</el-dialog>
</template>

<script>
import { getByPatientId, getPoverty } from "@/api/member/memberManagement";
import {
	allQueryMedicalRecord,
} from "@/api/outpatient/medicalRecord";
import UploadFile from "../../components/uploadFile.vue";
import { BigNumber } from "bignumber.js";
import { listDictItemAll } from "@/api/sys/dictItem";

export default {
	name: "patientDetail",
	components: {
		UploadFile,
	},
	data() {
		return {
			// 基础信息
			BasicInfoModel: {
				name: "",

				gender: {
					name: "",
				},
				age: "",
				month: "",
				phone: "",
				card: "",
				withPatientNexus: {
					name: "",
				},
				healthCardNo: "",
				pulse: "",
				breathe: "",
				temperature: "",
			},
			// 病历表单
			MedicalRecordModel: {},
			// 会员信息
			member: [],
			// 贫困信息
			poverty: [],
			// 治疗类型
			registration: {
				morbidityTime: "",
				treatType: {
					name: "初诊",
					value: "treatType_0",
				},
				infectType: {
					name: "否",
					value: "infectType_0",
				},
			},
			// 中药用法下拉列表
			ChineseUseOption: [],
			ChineseTimeOption: [],
			ChineseUseTimeOption: [],
			// 西药频度下拉列表
			FrequencyOption: [
				{
					name: "一天一次",
					value: "一天一次",
				},
				{
					name: "一天两次",
					value: "一天两次",
				},
				{
					name: "一天三次",
					value: "一天三次",
				},
				{
					name: "每周两次",
					value: "每周两次",
				},
			],
			dialogProps: {
				visible: false,
				title: '',
			},
			// 已就诊列表患者下拉部分点击区域
			AlreadyPatientDescriptions: [],
			// 文件上传
			MedicalFlags: "view",
			MedicalFileObjId: "",
			// 处方切换
			medicalEditTabsValue: {},
			isReadOnly: true // 禁用
		};
	},
	mounted: async function () {
		this.$nextTick(() => {
			this.$on("openViewPatientDetailDialog", function (row) {
				this.dialogProps.title = "详情";
				this.dialogProps.visible = true;
				// console.log('患者详情信息', row)

				this.GetAllOption();
				// 根据患者id获取会员信息
				getByPatientId(row.patid)
					.then((res) => {
						if (res.code == "100") {
							// console.log(res, "卡九分零四开发");
							this.member = res.data;
						} else {
							this.$message.error("后台数据异常请联系管理！");
						}
					})
					.catch();
				// 根据患者id获取贫困信息
				getPoverty(row.patid)
					.then((res) => {
						if (res.code == "100") {
							// console.log(res, "贫困户");
							this.poverty = res.data;
						} else {
							this.$message.error("后台数据异常请联系管理！");
						}
					})
					.catch();
				// 根据患者id获取详情信息
				allQueryMedicalRecord(row.regid).then((responseData) => {
					// console.log(arr, "这是怎么回事");
					if (responseData.code == 100) {
						console.log('患者详情', responseData)
						let recipelInfoEvtList = responseData.data.recipelInfoEvtList;
						recipelInfoEvtList.forEach((element) => {
							this.AlreadyPatientDescriptions.push(
								JSON.parse(JSON.stringify(element.recipelInfo))
							);
						});
						this.AlreadyPatientDescriptions.sort(function (a, b) {
							a.seq = a.seq ? a.seq : 0;
							b.seq = b.seq ? b.seq : 0;
							return a.seq - b.seq;
						});
						this.MedicalRecordModel = responseData.data.medicalRecord;
						this.MedicalRecordModel.registration.id =
							responseData.data.registration.id;
						this.MedicalRecordModel.registration.treatType =
							responseData.data.registration.treatType;
						this.MedicalRecordModel.registration.morbidityTime =
							responseData.data.registration.morbidityTime;

						this.MedicalRecordModel.registration.id = responseData.data.registration.id
						this.MedicalRecordModel.registration.infectType =
							responseData.data.registration.infectType;
						this.BasicInfoModel = responseData.data.patient;
						this.BasicInfoModel.breathe =
							responseData.data.registration.breathe;
						this.BasicInfoModel.temperature =
							responseData.data.registration.temperature;
						this.BasicInfoModel.pulse = responseData.data.registration.pulse;
						this.BasicInfoModel.bloodPressure =
							responseData.data.registration.bloodPressure;
						this.createMedicalEditTab(recipelInfoEvtList);

						this.changeMedicalFile(responseData.data.id);
					}
				});
			})
		})
	},
	methods: {
		// 附件上传切换
		changeMedicalFile(objectId) {
			// console.log(objectId, "我是附件上传的信息");
			// console.log('hint', this.hint)
			this.MedicalFileObjId = objectId;
			this.MedicalFlags = "view";
		},
		// 字典配置传参
		GetAllOption() {
			this.GetOption("1014474470772899981");
			this.GetOption("1014474470772899985");
			this.GetOption("1014474470772900028");
			this.GetOption("1014474470772900052");
			this.GetOption("1014474470772900062");
			this.GetOption("1014474470772900068");
			this.GetOption("1014474470772900058");
			this.GetOption("1088952622484547519");
			this.GetOption("1008534118685450388");
		},
		// 处方信息
		createMedicalEditTab(recipelInfoEvtList) {
			// console.log("---------recipelInfoEvtList------------");
			// console.log("处方信息", recipelInfoEvtList);
			this.medicalEditTabs = [];
			if (
				recipelInfoEvtList == undefined ||
				recipelInfoEvtList == null ||
				!recipelInfoEvtList.length > 0
			) {
				this.MedicalCalculate();
				return;
			} else {
				console.log("进入处方方法配置medicalEditTabs", this.medicalEditTabs);
				recipelInfoEvtList.forEach((element) => {
					element.uuid = element.recipelInfo.id;

					let costDebug = {
						chineseTest: [],
					};
					for (let i = 0; i < this.medicalEditTabs.length; i++) {
						for (
							let j = 0;
							j < this.medicalEditTabs[i].content.recipelDetailEvtList.length;
							j++
						) {
							costDebug.chineseTest.push(
								this.medicalEditTabs[i].content.recipelDetailEvtList[j]
									.drugStuffId.name
							);
						}
					}

					if (element.recipelInfo.recipelType.value == "recipelType_3") {
						this.inspectionType = [];
						this.inspectionSign = 0;

						for (let i = 0; i < element.recipelDetailEvtList.length; i++) {
							if (element.recipelDetailEvtList[i].drugStuffId.costItem) {
								if (
									element.recipelDetailEvtList[i].drugStuffId.costItem.itemType
										.value == "treatmentItemType_0" ||
									element.recipelDetailEvtList[i].drugStuffId.costItem.itemType
										.value == "treatmentItemType_1"
								) {
									this.inspectionSign = 1;
									this.inspectionType.push(0);
								}
							}
						}
						for (let i = 0; i < element.recipelDetailEvtList.length; i++) {
							if (
								element.recipelDetailEvtList[i].drugStuffId
									.inspectionCheckInfo != undefined
							) {
								if (
									element.recipelDetailEvtList[i].drugStuffId.costItem.itemType
										.value == "treatmentItemType_0" ||
									element.recipelDetailEvtList[i].drugStuffId.costItem.itemType
										.value == "treatmentItemType_1"
								) {
									console.log(
										element.recipelDetailEvtList[i].drugStuffId.costItem
											.inspectionCheckInfo,
										"进来了妈"
									);
									this.inspectionType[i] = 1;
								}
							}
						}
					}
					if (element.recipelInfo.recipelType.value == "recipelType_2") {
						let infusion = {
							defaultNumber: 1, //默认组号
							infusionProject: [[]],
							drippingSpeed: [""], //滴速
							days: [{}], //天数
							frequency: [{}], //频次
							infuseUse: [{}], //用法
							zushu: [1],
							excharge: [],
						};
						let count = 0;
						for (let i = 0; i < element.recipelDetailEvtList.length; i++) {
							if (element.recipelDetailEvtList[i].isExtra === 0) {
								if (count < element.recipelDetailEvtList[i].infuseGroup) {
									count = element.recipelDetailEvtList[i].infuseGroup;
								}
							}
						}

						for (let i = 1; i < count; i++) {
							infusion.defaultNumber = infusion.defaultNumber + 1;
							infusion.zushu.push(infusion.defaultNumber);
							infusion.infusionProject.push([]);
							infusion.drippingSpeed.push("");
							infusion.days.push({});
							infusion.frequency.push({});
							infusion.infuseUse.push({});
						}
						//this.excharge=null
						let arr = [];
						for (let i = 0; i < element.recipelDetailEvtList.length; i++) {
							if (element.recipelDetailEvtList[i].isExtra != 1) {
								infusion.infusionProject[
									element.recipelDetailEvtList[i].infuseGroup - 1
								].push(element.recipelDetailEvtList[i]);
								infusion.drippingSpeed[
									element.recipelDetailEvtList[i].infuseGroup - 1
								] = element.recipelDetailEvtList[i].drippingSpeed;
								infusion.days[element.recipelDetailEvtList[i].infuseGroup - 1] =
									element.recipelDetailEvtList[i].days;
								infusion.frequency[
									element.recipelDetailEvtList[i].infuseGroup - 1
								] = element.recipelDetailEvtList[i].frequency;
								infusion.infuseUse[
									element.recipelDetailEvtList[i].infuseGroup - 1
								] = element.recipelDetailEvtList[i].infuseUse;
							} else {
								infusion.excharge.push(element.recipelDetailEvtList[i]);
								//arr.push(element.recipelDetailEvtList[i])
							}
						}

						// this.excharge=arr
						let tab = {
							title: element.recipelInfo.name,
							key: element.recipelInfo.seq,
							content: element,
							closable: false,
							type: element.recipelInfo.recipelType.value,
							infusion: infusion,
							costDebug: costDebug,
						};
						this.medicalEditTabs.push(tab);
					} else {
						let tab = {
							title: element.recipelInfo.name,
							key: element.recipelInfo.seq,
							content: element,
							closable: false,
							type: element.recipelInfo.recipelType.value,
							costDebug: costDebug,
						};
						this.medicalEditTabs.push(tab);
					}
				});
				this.medicalEditTabsValue = this.medicalEditTabs[0];
				this.medicalClickTabsValue = this.medicalEditTabsValue;
				this.MedicalCalculate();
			}
		},
		// 金额计算
		MedicalCalculate() {
			let n1 = 0; //西药处方个数
			let n2 = 0; //中药处方个数
			let n3 = 0; //输液处方个数
			let n4 = 0; //诊疗项目处方个数
			let seq = 0; //处方顺序
			let fullAmount = 0; //所有处方的费用汇总
			// if(!this.medicalEditTabs.length>0){
			//   fullAmount=
			// }
			this.medicalEditTabs.forEach((tabElement) => {
				let medicalFullAmount = 0; //处方所有费用 = 处方费用汇总 + 附加费汇总
				let medicalAmount = 0; //处方费用汇总
				let surchargeAmount = 0; //附加费汇总
				let detailSeq = 0; //处方明细顺序
				let recipelInfo = tabElement.content.recipelInfo;
				let recipelTypeName = recipelInfo.recipelType.name;
				let recipelDetailEvtList = tabElement.content.recipelDetailEvtList
					? tabElement.content.recipelDetailEvtList
					: [];
				console.log("123", tabElement.content.recipelDetailEvtList);
				seq++;
				//西药处方
				if (tabElement.type === "recipelType_0") {
					n1++;
					tabElement.title = recipelTypeName + n1;
					recipelDetailEvtList.forEach((rowElement) => {
						if (rowElement.isExtra === 0) {
							detailSeq++;
							rowElement.seq = detailSeq;

							rowElement.singleDosage = rowElement.singleDosage
								? rowElement.singleDosage
								: "";
							rowElement.total = rowElement.total ? rowElement.total : 0;
							rowElement.allFee = rowElement.allFee ? rowElement.allFee : 0;
							if (rowElement.frequency && rowElement.days) {
								let total = Math.ceil(
									BigNumber(rowElement.singleDosage - 0)
										.multipliedBy(rowElement.frequency.value.split("_")[1])
										.multipliedBy(rowElement.days.name)
										.dividedBy(rowElement.drugStuffId.drug.dosis)
										.toNumber()
								);
								if (rowElement.isUnpackSell == "1") {
									rowElement.unitPrice = rowElement.drugStuffId.retailPrice;
									rowElement.total = total;
									rowElement.allFee = BigNumber(rowElement.total)
										.multipliedBy(rowElement.drugStuffId.retailPrice)
										.toNumber()
										.toFixed(2);
								} else {
									rowElement.unitPrice = rowElement.drugStuffId.price;
									rowElement.total = BigNumber(
										Math.ceil(
											BigNumber(total)
												.dividedBy(rowElement.drugStuffId.drug.preparation)
												.toNumber()
										)
									)
										.multipliedBy(rowElement.drugStuffId.drug.preparation)
										.toNumber();
									rowElement.allFee = BigNumber(
										Math.ceil(
											BigNumber(total)
												.dividedBy(rowElement.drugStuffId.drug.preparation)
												.toNumber()
										)
									)
										.multipliedBy(rowElement.drugStuffId.price)
										.toNumber();
								}
							}
							if (
								rowElement.drugStuffId.surplusStock < rowElement.total &&
								!this.isReadOnly &&
								tabElement.content.recipelInfo.dispensionStatus == 0
							) {
								if (!this.beyondInventoryType) {
									this.beyondInventoryType = true;
								}
								this.$message.error(
									rowElement.drugStuffId.name +
									"库存不足，请修改数量或者添加库存！"
								);
							} else {
								this.beyondInventoryType = false;
							}
							medicalAmount = BigNumber(medicalAmount)
								.plus(rowElement.allFee)
								.toNumber();
						}
					});
					medicalFullAmount = BigNumber(medicalFullAmount)
						.plus(medicalAmount)
						.toNumber();
				}
				//中药处方
				else if (tabElement.type === "recipelType_1") {
					n2++;
					tabElement.title = recipelTypeName + n2;
					recipelInfo.dosage = recipelInfo.dosage ? recipelInfo.dosage : "";
					recipelDetailEvtList.forEach((rowElement) => {
						if (rowElement.isExtra === 0) {
							detailSeq++;
							rowElement.seq = detailSeq;
							if (
								rowElement.singleDosage == " " ||
								rowElement.singleDosage == ""
							) {
								if (recipelInfo.dosage == "") {
									rowElement.singleDosage = "";
									rowElement.total = BigNumber(0)
										.multipliedBy(0)
										.multipliedBy(rowElement.drugStuffId.drug.preparation)
										.toNumber();
									rowElement.allFee = BigNumber(rowElement.drugStuffId.price)
										.multipliedBy(0)
										.multipliedBy(0)
										.toNumber();
									rowElement.unitPrice = rowElement.drugStuffId.price;

									medicalAmount = BigNumber(medicalAmount)
										.plus(rowElement.allFee)
										.toNumber();
								} else {
									rowElement.singleDosage = "";
									rowElement.total = BigNumber(0)
										.multipliedBy(recipelInfo.dosage)
										.multipliedBy(rowElement.drugStuffId.drug.preparation)
										.toNumber();
									rowElement.allFee = BigNumber(rowElement.drugStuffId.price)
										.multipliedBy(0)
										.multipliedBy(recipelInfo.dosage)
										.toNumber();
									rowElement.unitPrice = rowElement.drugStuffId.price;

									medicalAmount = BigNumber(medicalAmount)
										.plus(rowElement.allFee)
										.toNumber();
								}
							} else {
								if (recipelInfo.dosage == "") {
									rowElement.singleDosage = rowElement.singleDosage
										? rowElement.singleDosage - 0
										: 0;
									rowElement.total = BigNumber(rowElement.singleDosage)
										.multipliedBy(0)
										.multipliedBy(rowElement.drugStuffId.drug.preparation)
										.toNumber();
									rowElement.allFee = BigNumber(rowElement.drugStuffId.price)
										.multipliedBy(rowElement.singleDosage)
										.multipliedBy(0)
										.toNumber();
									rowElement.unitPrice = rowElement.drugStuffId.price;

									medicalAmount = BigNumber(medicalAmount)
										.plus(rowElement.allFee)
										.toNumber();
								} else {
									rowElement.singleDosage = rowElement.singleDosage
										? rowElement.singleDosage - 0
										: 0;
									rowElement.total = BigNumber(rowElement.singleDosage)
										.multipliedBy(recipelInfo.dosage)
										.multipliedBy(rowElement.drugStuffId.drug.preparation)
										.toNumber();
									rowElement.allFee = BigNumber(rowElement.drugStuffId.price)
										.multipliedBy(rowElement.singleDosage)
										.multipliedBy(recipelInfo.dosage)
										.toNumber();
									rowElement.unitPrice = rowElement.drugStuffId.price;

									medicalAmount = BigNumber(medicalAmount)
										.plus(rowElement.allFee)
										.toNumber();
								}
							}

							if (
								rowElement.drugStuffId.surplusStock < rowElement.total &&
								!this.isReadOnly &&
								tabElement.content.recipelInfo.dispensionStatus == 0
							) {
								if (!this.beyondInventoryType) {
									this.beyondInventoryType = true;
								}
								this.$message.error(
									rowElement.drugStuffId.name +
									"库存不足，请修改数量或者添加库存！"
								);
							} else {
								this.beyondInventoryType = false;
							}
						}
					});
					medicalFullAmount = BigNumber(medicalFullAmount)
						.plus(medicalAmount)
						.toNumber();
				}
				//输液处方
				else if (tabElement.type === "recipelType_2") {
					n3++;
					tabElement.title = recipelTypeName + n3;
					//  tabElement.content.recipelDetailEvtList=this.infusionProject

					let arr = 0;
					tabElement.content.recipelDetailEvtList = [];
					for (let i = 0; i < tabElement.infusion.infusionProject.length; i++) {
						for (
							let j = 0;
							j < tabElement.infusion.infusionProject[i].length;
							j++
						) {
							tabElement.infusion.infusionProject[i][j].drippingSpeed =
								tabElement.infusion.drippingSpeed[
								tabElement.infusion.infusionProject[i][j].infuseGroup - 1
								];
							tabElement.infusion.infusionProject[i][j].days =
								tabElement.infusion.days[
								tabElement.infusion.infusionProject[i][j].infuseGroup - 1
								];
							tabElement.infusion.infusionProject[i][j].frequency =
								tabElement.infusion.frequency[
								tabElement.infusion.infusionProject[i][j].infuseGroup - 1
								];
							tabElement.infusion.infusionProject[i][j].infuseUse =
								tabElement.infusion.infuseUse[
								tabElement.infusion.infusionProject[i][j].infuseGroup - 1
								];
							tabElement.content.recipelDetailEvtList[arr] =
								tabElement.infusion.infusionProject[i][j];
							arr++;
						}
					}
					// let countArr=0
					// countArr=arr
					// for (let i = 0; i < this.excharge.length; i++) {
					//   tabElement.content.recipelDetailEvtList.push(this.excharge[i])
					//  // tabElement.content.recipelDetailEvtList[countArr+1]=this.excharge[i]
					//   //countArr++;
					// }
					for (let i = 0; i < tabElement.infusion.excharge.length; i++) {
						tabElement.content.recipelDetailEvtList.push(
							tabElement.infusion.excharge[i]
						);
					}
					recipelDetailEvtList = tabElement.content.recipelDetailEvtList
						? tabElement.content.recipelDetailEvtList
						: [];
					console.log(this.medicalEditTabs, "kankan");

					//  recipelInfo.frequency = recipelInfo.frequency ? recipelInfo.frequency : 0;
					let infusionRecipelDetailEvtList =
						tabElement.content.recipelDetailEvtList;
					infusionRecipelDetailEvtList.forEach((rowElement) => {
						if (rowElement.isExtra === 0) {
							detailSeq++;
							rowElement.seq = detailSeq;
							rowElement.singleDosage = rowElement.singleDosage
								? rowElement.singleDosage
								: "";
							rowElement.total = rowElement.total ? rowElement.total : 0;
							rowElement.allFee = rowElement.allFee ? rowElement.allFee : 0;
							if (rowElement.frequency.value && rowElement.days.name) {
								let total = Math.ceil(
									BigNumber(rowElement.singleDosage - 0)
										.multipliedBy(rowElement.frequency.value.split("_")[1])
										.multipliedBy(rowElement.days.name)
										.dividedBy(rowElement.drugStuffId.drug.dosis)
										.toNumber()
								);
								if (rowElement.isUnpackSell == "1") {
									rowElement.unitPrice = rowElement.drugStuffId.retailPrice;
									rowElement.total = total;
									rowElement.allFee = BigNumber(rowElement.total)
										.multipliedBy(rowElement.drugStuffId.retailPrice)
										.toNumber();
								} else {
									rowElement.unitPrice = rowElement.drugStuffId.price;
									rowElement.total = BigNumber(
										Math.ceil(
											BigNumber(total)
												.dividedBy(rowElement.drugStuffId.drug.preparation)
												.toNumber()
										)
									)
										.multipliedBy(rowElement.drugStuffId.drug.preparation)
										.toNumber();
									rowElement.allFee = BigNumber(
										Math.ceil(
											BigNumber(total)
												.dividedBy(rowElement.drugStuffId.drug.preparation)
												.toNumber()
										)
									)
										.multipliedBy(rowElement.drugStuffId.price)
										.toNumber();
								}
							}
							if (
								rowElement.drugStuffId.surplusStock < rowElement.total &&
								!this.isReadOnly &&
								tabElement.content.recipelInfo.dispensionStatus == 0
							) {
								if (!this.beyondInventoryType) {
									this.beyondInventoryType = true;
								}
								this.$message.error(
									rowElement.drugStuffId.name +
									"库存不足，请修改数量或者添加库存！"
								);
							} else {
								this.beyondInventoryType = false;
							}
							medicalAmount = BigNumber(medicalAmount)
								.plus(rowElement.allFee)
								.toNumber();
						}
					});
					medicalFullAmount = BigNumber(medicalFullAmount)
						.plus(medicalAmount)
						.toNumber();
				}
				//诊疗项目处方
				else if (tabElement.type === "recipelType_3") {
					n4++;
					tabElement.title = recipelTypeName + n4;
					recipelDetailEvtList.forEach((rowElement) => {
						if (rowElement.isExtra === 0) {
							detailSeq++;
							rowElement.seq = detailSeq;
							rowElement.total = rowElement.total ? rowElement.total : "";
							rowElement.allFee = BigNumber(rowElement.drugStuffId.price)
								.multipliedBy(rowElement.total - 0)
								.toNumber();
							rowElement.unitPrice = rowElement.drugStuffId.price;

							medicalAmount = BigNumber(medicalAmount)
								.plus(rowElement.allFee)
								.toNumber();
						}
					});
					medicalFullAmount = BigNumber(medicalFullAmount)
						.plus(medicalAmount)
						.toNumber();
				} else if (tabElement.type === "recipelType_5") {
					n1++;
					tabElement.title = recipelTypeName + n1;
					recipelDetailEvtList.forEach((rowElement) => {
						// if (rowElement.isExtra === 0) {
						//   detailSeq++;
						//   rowElement.seq = detailSeq;
						//   rowElement.total = rowElement.total ? rowElement.total : "";
						//   rowElement.allFee = BigNumber(rowElement.drugStuffId.price)
						//     .multipliedBy(rowElement.total-0)
						//     .toNumber();
						//   rowElement.unitPrice = rowElement.drugStuffId.price;

						medicalAmount = BigNumber(medicalAmount)
							.plus(rowElement.allFee)
							.toNumber();
						// }
					});
					medicalFullAmount = BigNumber(medicalFullAmount)
						.plus(medicalAmount)
						.toNumber();
				}

				//每个处方的附加费
				recipelDetailEvtList.forEach((rowElement) => {
					// if(rowElement.infuseGroup==undefined){
					if (rowElement.isExtra === 1) {
						detailSeq++;
						rowElement.seq = detailSeq;
						// rowElement.singleDosage =
						//   rowElement.singleDosage && rowElement.singleDosage != 0 ?
						//     rowElement.singleDosage :
						//     "";
						if (rowElement.isUnpackSell == "1") {
							rowElement.total = rowElement.singleDosage - 0;
							rowElement.allFee = BigNumber(rowElement.drugStuffId.retailPrice)
								.multipliedBy(rowElement.total)
								.toNumber();
							rowElement.unitPrice = rowElement.drugStuffId.retailPrice;
							console.log(rowElement, "奇怪");
							if (
								rowElement.drugStuffId.surplusStock < rowElement.total &&
								!this.isReadOnly &&
								tabElement.content.recipelInfo.dispensionStatus == 0
							) {
								if (!this.beyondInventoryType) {
									this.beyondStuffInventoryType = true;
								}
								this.$message.error(
									rowElement.drugStuffId.name +
									"库存不足，请修改数量或者添加库存！"
								);
							} else {
								this.beyondStuffInventoryType = false;
							}
						} else {
							//材料
							if (rowElement.stuffType == "4") {
								rowElement.total = BigNumber(rowElement.singleDosage - 0)
									.multipliedBy(rowElement.packNumber)
									.toNumber();

								if (
									rowElement.drugStuffId.surplusStock < rowElement.total &&
									!this.isReadOnly &&
									tabElement.content.recipelInfo.dispensionStatus == 0
								) {
									if (!this.beyondInventoryType) {
										this.beyondStuffInventoryType = true;
									}
									this.$message.error(
										rowElement.drugStuffId.name +
										"库存不足，请修改数量或者添加库存！"
									);
								} else {
									this.beyondStuffInventoryType = false;
								}
							}
							//诊疗项目
							else if (rowElement.stuffType == "3") {
								rowElement.total = rowElement.singleDosage - 0;
							}

							rowElement.unitPrice = rowElement.drugStuffId.price;
							rowElement.allFee = BigNumber(rowElement.singleDosage - 0)
								.multipliedBy(rowElement.drugStuffId.price)
								.toNumber();
						}

						surchargeAmount = BigNumber(surchargeAmount)
							.plus(rowElement.allFee)
							.toNumber();
					}
					//}
				});
				console.log('recipelDetailEvtList', recipelDetailEvtList)
				medicalFullAmount = BigNumber(medicalFullAmount)
					.plus(surchargeAmount)
					.toNumber();

				recipelInfo.name = tabElement.title;
				recipelInfo.seq = seq;
				recipelInfo.medicalAmount = medicalAmount;
				recipelInfo.surchargeAmount = surchargeAmount;
				recipelInfo.fee = medicalFullAmount;
				fullAmount = BigNumber(fullAmount).plus(recipelInfo.fee).toNumber();
			});

			this.payAmount = fullAmount;
		},
		// table过滤
		getDataFilterTable(data, isExtra, item) {
			let arr = [];
			if (data.length == undefined) {
				arr.push(data);
			} else if (data.length >= 1) {
				for (let i = 0; i < data.length; i++) {
					if (data[i].isExtra == 0 && data[i].drugStuffId.drug) {
						if (data[i].drugStuffId.drug.type.value == "medicalType_0") {
							//   if(data[i].singleDosage==0&&data[i].singleDosage==" "&&data[i].drugStuffId.drug.singleDosage){
							//   data[i].singleDosage = data[i].drugStuffId.drug.singleDosage
							// }
							//console.log(item,'这是一个新址');
							if (
								data[i].singleDosage == 0 &&
								data[i].singleDosage == " " &&
								data[i].drugStuffId.drug.singleDosage
							) {
								let flages = false;
								for (let i = 0; i < item.chineseTest.length; i++) {
									if (item.chineseTest[i] == data[i].drugStuffId.drug.name) {
										flages = true;
										break;
									}
								}
								if (!flages) {
									data[i].singleDosage = data[i].drugStuffId.drug.singleDosage
										? data[i].drugStuffId.drug.singleDosage
										: "";
									item.chineseTest.push(data[i].drugStuffId.drug.name);
								}
							}
							if (
								!data[i].frequency &&
								data[i].drugStuffId.drug.frequency.value
							) {
								data[i].frequency = data[i].drugStuffId.drug.frequency;
							}
							if (!data[i].days && data[i].drugStuffId.drug.days.value) {
								data[i].days = data[i].drugStuffId.drug.days;
							}
							if (
								!data[i].westernMedicineUse &&
								data[i].drugStuffId.drug.westernMedicineUse.value
							) {
								data[i].westernMedicineUse =
									data[i].drugStuffId.drug.westernMedicineUse;
							}
						}
						if (data[i].drugStuffId.drug.type.value == "medicalType_1") {
							let flages = false;
							if (
								data[i].singleDosage == 0 &&
								data[i].singleDosage == " " &&
								data[i].drugStuffId.drug.singleDosage
							) {
								for (let i = 0; i < item.chineseTest.length; i++) {
									if (item.chineseTest[i] == data[i].drugStuffId.drug.name) {
										flages = true;
										break;
									}
								}
								if (!flages) {
									data[i].singleDosage = data[i].drugStuffId.drug.singleDosage
										? data[i].drugStuffId.drug.singleDosage
										: "";
									item.chineseTest.push(data[i].drugStuffId.drug.name);
								}
							}
							if (
								!data[i].chineseMedicineUse &&
								data[i].drugStuffId.drug.chineseMedicineUse.value
							) {
								data[i].chineseMedicineUse =
									data[i].drugStuffId.drug.chineseMedicineUse;
							}
						}
					}

					arr.push(data[i]);
				}
			}
			return arr.filter((item) => item.isExtra === isExtra);
			this.MedicalCalculate();
			return arr;
		},
		// 获取字典配置
		GetOption(optionId) {
			let model = {
				params: [
					{
						columnName: "dict_type_id",
						queryType: "=",
						value: optionId,
					},
				],
			};
			// /sys/dictItem/listAll
			listDictItemAll(model).then((responseData) => {
				if (optionId == "1014474470772899981") {
					this.ChineseUseOption = responseData.data;
					if (this.isSpecial) {
						this.ChineseUseOption = this.ChineseUseOption.filter(
							(item) => item.name == "水冲"
						);
					}
				} else if (optionId == "1014474470772899985")
					this.ChineseTimeOption = responseData.data;
				else if (optionId == "1014474470772900028")
					this.WesternUseOption = responseData.data;
				else if (optionId == "1014474470772900052")
					this.DayNumOption = responseData.data;
				else if (optionId == "1014474470772900062")
					this.infuseUseOption = responseData.data;
				else if (optionId == "1014474470772900068")
					this.InfusionOption = responseData.data;
				else if (optionId == "1014474470772900058")
					this.ChineseUseTimeOption = responseData.data;
				else if (optionId == "1088952622484547519")
					this.RecipelSmallTypeList = responseData.data;
				else if (optionId == "1008534118685450388") {
					this.TreatTypeOption = [];
					if (responseData.data) {
						responseData.data.forEach((element) => {
							this.TreatTypeOption.push({
								name: element.name,
								value: element.value,
							});
						});
					}
				} else if (optionId == "1654447687630167011") {
					this.InfectTypeOption = [];
					if (responseData.data) {
						responseData.data.forEach((element) => {
							this.InfectTypeOption.push({
								name: element.name,
								value: element.value,
							});
						});
					}
				}
			});
		},
	},
};

</script>

<style lang="scss" scoped>
.dialog-footer {
	float: right;
	padding-top: 20px;
}
</style>
<style>
.el-input-number {
	width: 1px;
	line-height: 10px;
	height: 28px;
}

.ones .el-table thead th {
	background-color: #f7f1f1 !important;
}

.two .el-table thead th {
	background-color: #e8fcfc !important;
}

.changeMedicalOutPatient .alreadyMedocal .el-collapse-item__header {
	border-bottom: 1px solid #dcdfe6;
	margin-right: 15px;
	background-color: rgb(234, 242, 251) !important;
}
</style>

<style scoped>
.shuzhuClass .el-input-number {
	width: 10% !important;
}

/deep/ .el-aside {
	/* background-color: #e6d6d3; */
	color: #333;
	/* text-align: center; */
	/* line-height: 200px; */
	margin-right: 5px;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
	height: calc(100vh - 118px);
}

/deep/ .el-main {
	/* background-color: #E9EEF3; */
	color: #333;
	/* text-align: center; */
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
	/* line-height: 200px; */
	height: calc(100vh - 118px);
}

/deep/ .el-card__header {
	padding: 12px 20px;
}

/deep/ .el-form-item--mini {
	margin-bottom: 14px !important;
}

/deep/ .main-card {
	margin-bottom: 10px;
}

/deep/ .main-card>.el-card__header {
	padding: 0px 20px;
}

/deep/ .main-card>.el-card__body {
	font-size: 14px;
}

/deep/ .sub-card>.el-card__body {
	padding: 12px 20px;
	font-size: 16px;
	font-weight: bold;
	color: midnightblue;
}

/deep/ .chinese-medicine-card>.el-card__header {
	padding: 0px 20px;
}

/deep/ .el-divider--horizontal {
	margin: 5px 0 10px 0px;
}

/deep/ .el-tabs__header {
	margin: 0 0 0;
}

/deep/ .el-collapse-item__content {
	padding-bottom: 0;
}

/deep/ .el-divider--vertical {
	width: 5px;
	height: 2em;
	margin-left: 0;
	background-color: #409eff;
}

/deep/ .el-input-group__append {
	padding: 4px;
}
</style>