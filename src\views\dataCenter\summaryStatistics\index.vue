<!--
 * @Author: xiang<PERSON>i
 * @Date: 2025-06-16 14:03:21
 * @Description: 中医-数据中台-数据统计-汇总统计
 * @type: page
-->

<template>
  <div>
    <el-card class="page-container">
      <div class="homeHeader">
        <template v-for="item in dataList">
          <el-card class="homeTitleAndNum">
            <div class="title">
              <div v-text="item.label"></div>
            </div>
            <div class="num">
              <div v-text="item.num"></div>
            </div>
          </el-card>
        </template>
      </div>
      <div class="homeContent2">
        <el-card class="item">
          <h3 class="text">门店会员充值统计</h3>
          <EchartComp class="homeEcharts2" :options="options2"></EchartComp>
        </el-card>
        <el-card class="item">
          <h3 class="text">门店消费统计</h3>
          <EchartComp class="homeEcharts3" :options="options3"></EchartComp>
        </el-card>
      </div>

      <div class="homeContent3">
        <Form
          class="demo-form-inline"
          ref="searchForm"
          :inline="true"
          :model="searchForm"
          :items="searchFormItems"
          :buttons="operationBtns"
          @search="handleSearch"
        />
      </div>

      <div class="homeContent4">
        <el-card class="item">
          <h3 class="text">总部收支流水</h3>
          <el-button
            class="export"
            size="mini"
            icon="el-icon-download"
            :loading="isExportLoading"
            @click="handleExport"
            >导出Excel</el-button
          >
          <el-button
            class="export"
            size="mini"
            icon="el-icon-printer"
            @click="handlePrint"
            >打印</el-button
          >

          <Table
            class="tableStyle"
            :tableData="table.tableList"
            :columns="table.columns"
            :total="table.total"
            :pagination="table.pagination"
            :loading="table.isTableLoading"
            :sizeChange="handleSizeChange"
            :currentChange="handleCurrentChange"
          />
        </el-card>
      </div>
    </el-card>
  </div>
</template>

<script>
import Form from "@/components/Combinecom/form";
import echarts from "echarts/lib/echarts";
import EchartComp from "../components/echartComp";
import Table from "@/components/Combinecom/table";
import Options from "../options";
import { handleFormatMoney } from "@/utils/validate";
import { downloadExcel } from "@/utils";
import API from "@/api/dataCenter";
import dayjs from "dayjs";

export default {
  name: "summaryStatistics",
  components: {
    EchartComp,
    Table,
    Form,
  },
  computed: {
    // 查询表单项
    searchFormItems() {
      return [
        {
          type: "text",
          size: "small",
          placeholder: "输入分店名称",
          label: "诊所名称",
          value: "searchName",
          limitLength: 20,
          clearable: true,
        },
        {
          type: "select",
          size: "small",
          placeholder: "请选择交易类型",
          label: "交易类型",
          value: "tollType",
          filterable: true,
          // clearable: true,
          // change: this.handleChangeType,
          options: Options.tradeTypeList,
        },
        {
          type: "datetimerange",
          size: "small",
          label: "选择时间",
          placeholder: "请选择日期",
          value: "rangeTime",
          valueFormat: "yyyy-MM-dd HH:mm:ss",
        },
      ];
    },
    // 查询区按钮配置项
    operationBtns() {
      return [
        {
          class: "is-plain",
          type: "primary",
          size: "small",
          label: "搜索",
          icon: "el-icon-search",
          click: () => this.handleSearch(),
        },
        {
          class: "is-plain",
          type: "info",
          size: "small",
          label: "重置",
          icon: "el-icon-refresh-left",
          click: () => this.handleReset(),
        },
      ];
    },
  },
  data() {
    return {
      // 查询条件栏
      searchForm: {
        searchName: "",
        tollType: "",
        startDate: "",
        endDate: "",
        rangeTime: [],
      },
      dataList: [
        {
          label: "今日营业额(元)",
          num: 0,
          value: "totalIncome",
          isLoading: true,
        },
        {
          label: "会员数(个)",
          num: 0,
          value: "memberCount",
          isLoading: true,
        },
        {
          label: "资金结算总额(元)",
          num: 0,
          value: "totalSettleAmt",
          isLoading: true,
        },
        {
          label: "奖金分配总额(元)",
          num: 0,
          value: "totalBonusAmt",
          isLoading: true,
        },
      ],
      options2: {}, // 纵向柱状图  会员卡充值
      options3: {}, // 横向柱状图  药品销售
      chartData2: {
        /** 会员充值 */ dates: [],
        amounts: [],
      },
      /** 门店消费 */
      chartData3: {
        medicines: [],
        sales: [],
      },
      // 表数据
      table: {
        // table参数
        total: 0,
        tableList: [],
        pagination: {
          limit: 20,
          offset: 0,
        },
        columns: [
          {
            label: "序号",
            type: "index",
          },
          {
            label: "日期",
            prop: "tradeDate",
          },
          {
            label: "流向",
            prop: "tradeDirection",
          },
          {
            label: "金额(元)",
            prop: "tradeAmt",
            formatter: (row) => {
              return handleFormatMoney(row.tradeAmt);
            },
          },
          {
            label: "交易类型",
            prop: "tradeTypeValue",
          },
          {
            label: "所属门店",
            prop: "companyName",
          },
        ],
        isTableLoading: false, // 表格loading
      },

      isExportLoading: false, // 导出loading
    };
  },
  mounted() {
    this.getAllStatistics();
  },
  methods: {
    // 查询
    handleSearch() {
      this.getTableData();
    },
    // 重置
    handleReset() {
      this.searchForm.searchName = null;
      this.searchForm.tollType = null;
      this.searchForm.rangeTime = null;

      this.getTableData();
    },
    // 打印
    handlePrint() {
      const str = `&searchName=${this.searchForm.searchName}&tollType=${
        this.searchForm.tollType
      }&startDate=${
        this.searchForm.rangeTime.length ? this.searchForm.rangeTime[0] : ""
      }&endDate=${
        this.searchForm.rangeTime.length ? this.searchForm.rangeTime[1] : ""
      }&companyId=${currentUser.company.id}`;

      const url = `${process.env.BASE_API}/ureport/preview?_u=Newtouch:flowingCapital.ureport.xml&_t=0${str}`;
      // console.log('url', url)
      // return
      window.open(url);
    },
    // total
    handleSizeChange(val) {
      // console.log('val', val)
      this.table.pagination.offset = val;
      this.getTableData();
    },
    // 改变页数
    handleCurrentChange(val) {
      // console.log('val2222', val)
      this.table.pagination.offset = val;
      this.getTableData();
    },
    // 配置图表
    handleConfigEcharts() {
      this.options2 = {
        tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
          formatter: function (params) {
            let result = params[0].name + "<br/>";
            params.forEach(function (item) {
              result += `${item.seriesName}: ${handleFormatMoney(
                item.value
              )}<br/>`;
            });
            return result;
          },
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true,
        },
        xAxis: {
          type: "category",
          // name: '(日)',
          // nameLocation: 'end',
          // nameTextStyle: {
          // 	margin: [40, 0, 0, 0] // 调整位置
          // },
          axisLine: {
            lineStyle: {
              color: "#999",
            },
          },
          axisTick: {
            alignWithLabel: false,
          },
          axisLabel: {
            color: "#B2B3B5",
            padding: [10, 0, 0, 0],
          },
          data: this.chartData2.dates,
        },
        yAxis: {
          type: "value",
          name: "(元)",
          nameTextStyle: {
            padding: [0, 60, 0, 10], // 调整位置
          },
          // min: 0,
          // max: 12000,
          // interval: 2000,
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: "#B2B3B5",
            padding: [0, 10, 0, 0],
          },
          splitLine: {
            lineStyle: {
              type: "solid",
              color: "#E8ECF4",
            },
          },
        },
        series: [
          {
            name: "充值金额",
            type: "bar",
            barWidth: "80%",
            data: this.chartData2.amounts,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: "#0090B6" },
                { offset: 0.5, color: "#00A8B5" },
                { offset: 1, color: "#27A59F" },
              ]),
            },
            emphasis: {},
          },
        ],
      };
      this.options3 = {
		tooltip: {
          trigger: "axis",
          axisPointer: {
            type: "shadow",
          },
          formatter: function (params) {
            let result = params[0].name + "<br/>";
            params.forEach(function (item) {
              result += `${
                item.seriesName
              }: ${handleFormatMoney(item.value)}<br/>`;
            });
            return result;
          },
        },
        grid: {
          left: "3%",
          right: "10%",
          top: "3%",
          bottom: "3%",
          containLabel: true,
        },
        yAxis: {
          type: "category",
          data: this.chartData3.medicines,
          axisLine: {
            lineStyle: {
              color: "#999",
            },
          },
          axisTick: {
            alignWithLabel: false,
          },
          axisLabel: {
            color: "#B2B3B5",
            padding: [0, 10, 0, 0],
          },
        },
        xAxis: {
          type: "value",
          min: 0,
          max: 120,
          interval: 20,
          axisLine: {
            show: false,
          },
          axisTick: {
            show: false,
          },
          axisLabel: {
            color: "#B2B3B5",
            padding: [10, 0, 0, 0],
          },
          splitLine: {
            lineStyle: {
              type: "solid",
              color: "#E8ECF4",
            },
          },
        },
        series: [
          {
            name: "消费金额",
            type: "bar",
            barWidth: "60%",
            data: this.chartData3.sales,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                { offset: 0, color: "#27A59F" },
                { offset: 0.5, color: "#00A8B5" },
                { offset: 1, color: "#0090B6" },
              ]),
            },
            label: {
              show: true,
              position: "right",
              formatter: "{c}",
              color: "#333",
            },
          },
        ],
      };
    },

    // 图表API
    async getAllStatistics() {
      Promise.all([
        this.getSummaryMember(),
        this.getSummaryTotal(),
        this.getConsumeData(),
        this.getTableData(),
      ])
        .then(() => {
          this.handleConfigEcharts();
        })
        .catch((err) => {
          this.$message.error(err);
        });
    },

    // 营业额、会员数、结算总额、分配总额
    async getSummaryTotal() {
      // const id = currentUser.company.id
      const id = currentUser.company.id;
      try {
        const res = await API.reqSummaryTotal(id);
        // console.log('id', res)
        if (res.code === "100") {
          this.dataList.map((v) => {
            for (let i in res.data) {
              if (v.value === i) {
                if (i === "memberCount") {
                  v.num = res.data[i];
                } else {
                  v.num = handleFormatMoney(res.data[i]);
                }
              }
            }
          });
        } else {
          this.$message.warning(res.msg);
        }
      } catch (error) {
        this.$message.error(error);
      }
    },

    // 门店会员充值
    async getSummaryMember() {
      const id = currentUser.company.id;
      try {
        const res = await API.reqSummaryMember(id);
        // console.log('id', res)
        if (res.code === "100") {
          this.chartData2.dates = res.data.map((v) =>
            dayjs(v.date).format("M-D")
          );
          this.chartData2.amounts = res.data.map((v) =>
            v.money ? v.money : 0
          );
          console.log("chartData2", this.chartData2);
        } else {
          this.$message.warning(res.msg);
        }
      } catch (error) {
        this.$message.error(error);
      }
    },

    // 门店消费统计
    async getConsumeData() {
      const id = currentUser.company.id;
      try {
        const res = await API.reqSummarySale(id);
        // console.log('res', res)
        if (res.code === "100") {
          this.chartData3.medicines = res.data.map((v) => v.companyName);
          this.chartData3.sales = res.data.map((v) =>
            v.salesMoney ? v.salesMoney : 0
          );
          // console.log('chartData2', this.chartData2)
        } else {
          this.$message.warning(res.msg);
        }
      } catch (error) {
        this.$message.error(error);
      }
    },

    // 导出
    async handleExport() {
      const data = {
        companyId: currentUser.company.id,
        searchName: this.searchForm.searchName,
        tollType: this.searchForm.tollType,
        startDate: this.searchForm.rangeTime
          ? this.searchForm.rangeTime[0]
          : "",
        endDate: this.searchForm.rangeTime ? this.searchForm.rangeTime[1] : "",
        limit: this.table.pagination.limit,
        offset: this.table.pagination.offset,
      };
      this.isExportLoading = true;

      try {
        const res = await API.reqSummaryExport(data);
        downloadExcel(res, "总部收支流水.xlsx");

        // console.log('res', res)
        this.$message.success("导出成功!");
        this.isExportLoading = false;
      } catch (error) {
        this.isExportLoading = false;
        this.$message.error(error);
      }
    },

    // table数据(总部收支流水)
    async getTableData() {
      const data = {
        companyId: currentUser.company.id,
        searchName: this.searchForm.searchName,
        tollType: this.searchForm.tollType,
        startDate: this.searchForm.rangeTime
          ? this.searchForm.rangeTime[0]
          : "",
        endDate: this.searchForm.rangeTime ? this.searchForm.rangeTime[1] : "",
        limit: this.table.pagination.limit,
        offset: this.table.pagination.offset
          ? (this.table.pagination.offset - 1) * this.table.pagination.limit
          : 0,
      };
      // console.log('data', data)
      // return
      try {
        this.table.isTableLoading = true;
        const res = await API.reqSummaryFlowing(data);
        if (res.code === "100") {
          // 如果当前页无数据且不是第一页，则返回第一页
          if (!res.data.rows.length && this.table.pagination.offset > 0) {
            this.table.pagination.offset = 0;
            this.getTableData();
          }
          // console.log("xiangfei", res.data);
          this.table.tableList = res.data.rows || [];
          this.table.total = Number(res.data.total);
        } else {
          this.$message.warning(res.msg);
        }
        this.table.isTableLoading = false;
      } catch (error) {
        this.table.isTableLoading = false;
        this.$message.error(error);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.homeHeader {
  display: flex;
  font-size: 16px;

  .homeTitleAndNum {
    width: 40%;
    margin-right: 10px;

    .title {
      font-size: 16px;
      color: #6c6c6c;
    }

    .num {
      font-size: 28px;
      color: #000000;
    }
  }
}

.homeHeader .homeTitleAndNum:last-child {
  margin-right: 0;
}

.homeContent2 {
  box-sizing: content-box;
  display: flex;
  margin-top: 20px;

  .item {
    width: 50%;
    margin-right: 10px;
    height: 320px;

    .text {
      font-size: 16px;
      color: #333;
      margin: 0;
    }

    .homeEcharts0,
    .homeEcharts1,
    .homeEcharts2,
    .homeEcharts3,
    /deep/ .el-card__body {
      height: 270px;
    }

    /deep/ .custom-tooltip-style {
      padding: 0 16px;
      width: auto;
      height: 48px;
      line-height: 48px;
      background-color: #ffffff;
      text-align: center;
      box-shadow: 0px 0px 8px 0px rgba(0, 0, 0, 0.0999);
      border-radius: 2px 2px 2px 2px;
    }
  }
}

.homeContent2 .item:last-child {
  margin-right: 0;
}

.homeContent3 {
  padding-top: 15px;
  box-sizing: border-box;
  display: flex;
  margin-top: 20px;
  width: 100%;

  .demo-form-inline {
    padding-left: 25px;
  }
}

.homeContent4 {
  box-sizing: border-box;
  display: flex;
  margin-top: 20px;
  width: 100%;

  .item {
    width: 100%;

    .text {
      display: inline-block;
      font-size: 16px;
      color: #333;
      margin: 0;
    }

    .export {
      float: right;
      margin-left: 10px;
    }
  }

  .tableStyle {
    margin-top: 20px;
  }
}
</style>
