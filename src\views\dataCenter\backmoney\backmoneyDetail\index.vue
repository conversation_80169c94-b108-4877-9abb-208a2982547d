<!--
 * @Author: x<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-16 14:03:21
 * @Description: 中医-数据中台-数据统计-退卡结算-详情
 * @type: page
-->

<template>
	<div>
		<el-card class="page-container">
			<el-button size="small" icon="el-icon-back" class="HomeBackBtn" @click="handleBack()">返回</el-button>

			<div class="homeContent">
				<el-card class="item">
					<h3 class="text">会员退卡详情</h3>
					<Table class="tableStyle" :tableData="table.tableList" :columns="table.columns" :total="table.total"
						:pagination="false" :loading="table.isTableLoading">
						<el-table-column slot="refundStatus" prop="refundStatus" label="结算状态">
							<template slot-scope="scope">
								<div class="tagStyle">
									<el-tag :type="Number(scope.row.refundStatus) === 1 ? 'success' : 'warning'">{{
										Number(scope.row.refundStatus) === 1 ? '已结算' : '待结算'
									}}</el-tag>
								</div>
							</template>
						</el-table-column>

					</Table>
				</el-card>
			</div>
		</el-card>
	</div>
</template>

<script>

import Table from "@/components/Combinecom/table";
import { handleFormatMoney } from '@/utils/validate'
import API from "@/api/dataCenter";

export default {
	name: "backmoneyDetail",
	components: { Table },
	computed: {},
	data() {
		return {
			searchForm: {
				searchName: null,
				companyId: null
			},
			// 表数据
			table: {
				// table参数
				total: 0,
				tableList: [],
				pagination: {
					limit: 20,
					offset: 0
				},
				columns: [
					{
						label: "分店名称",
						prop: "company.name"
					},
					{
						label: "客户名称",
						prop: "memberName"
					},
					{
						label: "分公司结算给总公司的金额(元)",
						prop: "refundMoney",
						formatter: row => {
							return handleFormatMoney(row.refundMoney)
						}
					},
					{
						slot: "refundStatus"
					},
					{
						label: "结算日期",
						prop: "refundDate"
					},
					{
						label: '操作',
						minWidth: 120,
						buttons: [
							{
								label: '结算',
								type: 'text',
								// disabled: true,
								click: row => this.handleSend(row),
								hidden: row => Number(row.refundStatus) === 1
							},
						]
					}
				],
				isTableLoading: false, // 表格loading

			},
		};
	},
	mounted() {
		this.getTableData();
	},
	methods: {
		// 返回列表页
		handleBack() {
			this.$router.push({
				path: '/backmoney',
			})
		},
		// 结算
		handleSend(row) {
			// console.log('row', row)
			this.$confirm('该操作不可撤销，确认结算选中的数据吗？', '确认', {
				confirmButtonText: '确定',
				cancelButtonText: '取消',
				type: 'warning'
			}).then(() => {
				this.getSendData(row)
			}).catch(() => {
				this.$message.info("已取消")
			})
		},

		// 结算API
		async getSendData(row) {

			try {
				const res = await API.reqBackHeadData(row.memberManagementId, row.company.id);
				if (res.code === '100') {
					this.$message.success('结算成功!');
					this.getTableData()
				} else {
					this.$message.warning(res.msg);
				}
			} catch (error) {
				this.$message.error(error);
			}

		},

		async getTableData() {
			// console.log('id', this.$route.query.id)
			try {
				this.table.isTableLoading = true;
				const res = await API.reqBackDetail(this.$route.query.id);
				if (res.code === '100') {
					this.table.tableList = res.data || [];
				} else {
					this.$message.warning(res.msg);
				}
				this.table.isTableLoading = false;
			} catch (error) {
				this.table.isTableLoading = false;
				this.$message.error(error);
			}
		}
	},
};
</script>

<style lang="scss" scoped>
.HomeBackBtn {
	position: relative;
	top: -20px;
}

.homeContent {
	box-sizing: border-box;
	display: flex;
	margin-top: 20px;
	width: 100%;

	.item {
		width: 100%;

		.text {
			display: inline-block;
			font-size: 16px;
			color: #333;
			margin: 0;
		}

		.export {
			float: right;
		}
	}

	.tableStyle {
		margin-top: 20px;
	}
}

.tagStyle {
	text-align: center;
}
</style>