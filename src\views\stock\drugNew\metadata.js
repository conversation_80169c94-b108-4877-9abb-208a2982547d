const metadata = [
	{
        id: '1004078055755374623',
        schemeId: '1004078055755374645',
        name: '药品信息',
        dataRules:[]
    }
]

const tenantList = [
  {
      "id": "2086093515796856877",
      "userId": "2084869364217815460",
      "routerId": "2077468568630584869",
      "tableId": "1004078055755374623",
      "prop": "goodsName",
      "label": "药品名称",
      "align": "left",
      "miniWidth": 160,
      "width": null,
      "display": true,
      "sort": 20000,
      "showType": "SingleInput",
      "javaType": "String"
  },
  {
      "id": "2086093515796856880",
      "userId": "2084869364217815460",
      "routerId": "2077468568630584869",
      "tableId": "1004078055755374623",
      "prop": "type.name",
      "label": "药品类型",
      "align": "center",
      "miniWidth": 100,
      "width": null,
      "display": true,
      "sort": 50000,
      "showType": "SingleInput",
      "javaType": "String"
  },
  {
      "id": "2086093515796856881",
      "userId": "2084869364217815460",
      "routerId": "2077468568630584869",
      "tableId": "1004078055755374623",
      "prop": "code",
      "label": "药品编码",
      "align": "left",
      "miniWidth": 120,
      "width": null,
      "display": true,
      "sort": 60000,
      "showType": "AutoSerial",
      "javaType": "String"
  },
//   {
//       "id": "2086093515796856882",
//       "userId": "2084869364217815460",
//       "routerId": "2077468568630584869",
//       "tableId": "1004078055755374623",
//       "prop": "source",
//       "label": "药品来源",
//       "align": "center",
//       "miniWidth": 180,
//       "width": null,
//       "display": true,
//       "sort": 70000,
//       "showType": "SingleInput",
//       "javaType": "String"
//   },
  {
      "id": "2086093515796856883",
      "userId": "2084869364217815460",
      "routerId": "2077468568630584869",
      "tableId": "1004078055755374623",
      "prop": "nature.name",
      "label": "性质",
      "align": "center",
      "miniWidth": 100,
      "width": null,
      "display": true,
      "sort": 80000,
      "showType": "SingleInput",
      "javaType": "String"
  },
  {
      "id": "2086093515796856884",
      "userId": "2084869364217815460",
      "routerId": "2077468568630584869",
      "tableId": "1004078055755374623",
      "prop": "factory.name",
      "label": "生产厂家",
      "align": "center",
      "miniWidth": 180,
      "width": null,
      "display": true,
      "sort": 90000,
      "showType": "SingleInput",
      "javaType": "String"
  },
  {
      "id": "2086093515796856885",
      "userId": "2084869364217815460",
      "routerId": "2077468568630584869",
      "tableId": "1004078055755374623",
      "prop": "standardCode",
      "label": "国药准字",
      "align": "center",
      "miniWidth": 120,
      "width": null,
      "display": true,
      "sort": 100000,
      "showType": "SingleInput",
      "javaType": "String"
  },
  {
      "id": "2086093515796856887",
      "userId": "2084869364217815460",
      "routerId": "2077468568630584869",
      "tableId": "1004078055755374623",
      "prop": "barCode",
      "label": "条形码",
      "align": "center",
      "miniWidth": 100,
      "width": null,
      "display": true,
      "sort": 120000,
      "showType": "SingleInput",
      "javaType": "String"
  },
  {
      "id": "2086093515796856893",
      "userId": "2084869364217815460",
      "routerId": "2077468568630584869",
      "tableId": "1004078055755374623",
      "prop": "createDate",
      "label": "创建时间",
      "align": "center",
      "miniWidth": 140,
      "width": null,
      "display": true,
      "sort": 180000,
      "showType": "DateTimePicker",
      "javaType": "java.util.Date"
  },
  {
      "id": "2086093515796856902",
      "userId": "2084869364217815460",
      "routerId": "2077468568630584869",
      "tableId": "1004078055755374623",
      "prop": "status",
      "label": "启用标志",
      "align": "center",
      "miniWidth": 100,
      "width": null,
      "display": true,
      "sort": 270000,
      "showType": "Switch",
      "javaType": "String"
  },
  {
      "prop": "syncNum",
      "label": "是否已同步",
      "align": "center",
      "miniWidth": 100,
      "width": null,
      "display": true,
      "sort": 270000,
      "showType": "Switch",
      "javaType": "String"
  },
]

const versionList = [
  {
  "prop": "goodsName",
  "label": "药品名称",
  "align": "center",
  "miniWidth": 200,
  "width": null,
  "display": true,
  },
//   {
//   "prop": "companyId",
//   "label": "诊所id",
//   "align": "center",
//   "miniWidth": 160,
//   "width": null,
//   "display": true,
//   },
  {
  "prop": "brandName",
  "label": "商品别名",
  "align": "center",
  "miniWidth": 160,
  "width": null,
  "display": true,
  },
  {
  "prop": "type",
  "label": "药品类型",
  "align": "center",
  "miniWidth": 160,
  "width": null,
  "display": true,
  },
  {
  "prop": "code",
  "label": "药品编码",
  "align": "center",
  "miniWidth": 160,
  "width": null,
  "display": true,
  },
//   {
//   "prop": "source",
//   "label": "药品来源",
//   "align": "center",
//   "miniWidth": 160,
//   "width": null,
//   "display": true,
//   },
  {
  "prop": "factory",
  "label": "生产单位",
  "align": "center",
  "miniWidth": 160,
  "width": null,
  "display": true,
  },
  {
  "prop": "pinyinCode",
  "label": "拼音",
  "align": "center",
  "miniWidth": 160,
  "width": null,
  "display": true,
  },
  {
  "prop": "price",
  "label": "价格",
  "align": "center",
  "miniWidth": 160,
  "width": null,
  "display": true,
  },
  {
  "prop": "dosis",
  "label": "剂量",
  "align": "center",
  "miniWidth": 160,
  "width": null,
  "display": true,
  },
//   {
//   "prop": "dosisUnit",
//   "label": "剂量单位",
//   "align": "center",
//   "miniWidth": 160,
//   "width": null,
//   "display": true,
//   },
  {
  "prop": "preparation",
  "label": "制剂",
  "align": "center",
  "miniWidth": 160,
  "width": null,
  "display": true,
  },
//   {
//   "prop": "preparationUnit",
//   "label": "制剂单位",
//   "align": "center",
//   "miniWidth": 160,
//   "width": null,
//   "display": true,
//   },
  {
  "prop": "pack",
  "label": "包装单位",
  "align": "center",
  "miniWidth": 160,
  "width": null,
  "display": true,
  },
  {
"prop": "insuranceCode",
  "label": "医保编码",
  "align": "center",
  "miniWidth": 160,
  "width": null,
  "display": true,
  },
{
"prop": "standardCode",
  "label": "国药准字",
  "align": "center",
  "miniWidth": 160,
  "width": null,
  "display": true,
  },
  {
  "prop": "status",
  "label": "状态",
  "align": "center",
  "miniWidth": 160,
  "width": null,
  "display": true,
  },
]

export { metadata, tenantList ,versionList }
