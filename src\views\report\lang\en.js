export default {
  fu: {
    search_bar: {
      search: 'Search',
      adv_search: 'Advanced search',
      ok: 'Confirm',
      cancel: 'Cancel',
      please_select: 'Please select',
      please_input: 'Please input',
      like: 'like ',
      not_like: 'not like',
      in: 'in',
      not_in: 'not in',
      gt: 'greater than',
      ge: 'Greater than or equal to',
      lt: 'less than',
      le: 'Less than or equal to',
      eq: 'equal to',
      ne: 'bot equal to',
      between: 'between',
      select_date: 'Select a date',
      start_date: 'Start date',
      end_date: 'End date',
      select_date_time: 'Select date time',
      start_date_time: 'Start date time',
      end_date_time: 'End date time',
      range_separator: 'To',
      data_time_error: 'The start date cannot be greater than the end date',
      clean: 'Clean',
      refresh: 'Refresh'
    },
    table: {
      ok: 'Confirm',
      custom_table_fields: 'Custom table fields',
      custom_table_fields_desc: 'Fixed field is not in the selection range'
    },
    steps: {
      cancel: 'Cancle',
      next: 'next',
      prev: 'Last step',
      finish: 'Finish'
    }
  },
  route: {
    dashboard: 'Dashboard',
    documentation: 'Documentation',
    guide: 'Guide',
    permission: 'Permission',
    pagePermission: 'Page Permission',
    rolePermission: 'Role Permission',
    directivePermission: 'Directive Permission',
    icons: 'Icons',
    components: 'Components',
    tinymce: 'Tinymce',
    markdown: 'Markdown',
    jsonEditor: 'JSON Editor',
    dndList: 'Dnd List',
    splitPane: 'SplitPane',
    avatarUpload: 'Avatar Upload',
    dropzone: 'Dropzone',
    sticky: 'Sticky',
    countTo: 'Count To',
    componentMixin: 'Mixin',
    backToTop: 'Back To Top',
    dragDialog: 'Drag Dialog',
    dragSelect: 'Drag Select',
    dragKanban: 'Drag Kanban',
    charts: 'Charts',
    keyboardChart: 'Keyboard Chart',
    lineChart: 'Line Chart',
    mixChart: 'Mix Chart',
    example: 'Example',
    nested: 'Nested Routes',
    menu1: 'Menu 1',
    'menu1-1': 'Menu 1-1',
    'menu1-2': 'Menu 1-2',
    'menu1-2-1': 'Menu 1-2-1',
    'menu1-2-2': 'Menu 1-2-2',
    'menu1-3': 'Menu 1-3',
    menu2: 'Menu 2',
    Table: 'Table',
    dynamicTable: 'Dynamic Table',
    dragTable: 'Drag Table',
    inlineEditTable: 'Inline Edit',
    complexTable: 'Complex Table',
    tab: 'Tab',
    form: 'Form',
    createArticle: 'Create Article',
    editArticle: 'Edit Article',
    articleList: 'Article List',
    errorPages: 'Error Pages',
    page401: '401',
    page404: '404',
    errorLog: 'Error Log',
    excel: 'Excel',
    exportExcel: 'Export Excel',
    selectExcel: 'Export Selected',
    mergeHeader: 'Merge Header',
    uploadExcel: 'Upload Excel',
    zip: 'Zip',
    pdf: 'PDF',
    exportZip: 'Export Zip',
    theme: 'Theme',
    clipboardDemo: 'Clipboard',
    i18n: 'I18n',
    externalLink: 'External Link',
    profile: 'Profile'
  },
  navbar: {
    dashboard: 'Dashboard',
    github: 'Github',
    logOut: 'Log Out',
    profile: 'Profile',
    theme: 'Theme',
    size: 'Global Size'
  },
  login: {
    title: 'Login',
    welcome: 'Welcome To ',
    logIn: 'Login',
    username: 'Username',
    password: 'Password',
    any: 'any',
    thirdparty: 'Or connect with',
    thirdpartyTips: 'Can not be simulated on local, so please combine you own business simulation! ! !',
    expires: 'Login token expired, please login again',
    tokenError: 'Token error, please login again',
    username_error: 'Please enter the correct ID',
    password_error: 'The password can not be less than 8 digits',
    re_login: 'Login again',
    default_login: 'Normal'
  },
  commons: {
    search: 'Search',
    folder: 'Folder',
    no_target_permission: 'No permission',
    success: 'Success',
    switch_lang: 'Switch Language Success',
    close: 'Close',
    icon: 'Icon',
    all: 'All',
    enable: 'Enable',
    disable: 'Disable',
    yes: 'Yes',
    no: 'No',
    reset: 'Reset',
    gender: 'Gender',
    catalogue: 'Catalogue',
    button: 'Button',
    man: 'Man',
    woman: 'Woman',
    nick_name: 'Name',
    confirmPassword: 'Confirm Password',
    upload: 'Upload',
    cover: 'Cover',
    not_cover: 'Not Cover',
    import_mode: 'Import Mode',
    import_module: 'Import module',
    please_fill_in_the_template: 'Please fill in the template',
    cut_back_old_version: 'Switch back to the old version',
    cut_back_new_version: 'Switch back to the new version',
    comment: 'Comments',
    examples: 'Examples',
    help_documentation: 'Help document',
    api_help_documentation: 'API document',
    delete_cancelled: 'Deletion canceled',
    workspace: 'Workspace',
    organization: 'Organization',
    menu: 'Menu',
    setting: 'Setting',
    project: 'Project',
    about_us: 'About Us',
    current_project: 'Current Project',
    name: 'Name',
    description: 'Description',
    annotation: 'Annotation',
    clear: 'Clean',
    save: 'Save',
    update: 'Update',
    save_success: 'Saved successfully',
    delete_success: 'deleted successfully',
    copy_success: 'Copy succeeded',
    modify_success: 'Modified successfully',
    delete_cancel: 'Deletion canceled',
    confirm: 'Confirm',
    cancel: 'Cancel',
    prompt: 'Tips',
    operating: 'Operation',
    input_limit: 'The length is between {0} and {1} characters',
    login: 'Login',
    welcome: 'One stop open source data analysis platform',
    username: 'User Name',
    password: 'Pass word',
    input_username: 'Please enter the user name',
    input_password: 'Please input a password',
    test: 'Test',
    create_time: 'Create Time',
    update_time: 'Update Time',
    add: 'Add',
    member: 'Member',
    email: 'Email',
    phone: 'Phone',
    role: 'Role',
    personal_info: 'Personal Info',
    api_keys: 'API Keys',
    quota: 'Quota',
    status: 'Status',
    show_all: 'Show All',
    show: 'Show',
    report: 'Report',
    user: 'User',
    system: 'System',
    personal_setting: 'Personal Setting',
    test_resource_pool: 'Test Resource Pool',
    system_setting: 'System Setting',
    input_content: 'Please input the content',
    create: 'Create',
    edit: 'Edit',
    copy: 'Copy',
    refresh: 'Refresh',
    remark: 'Remark',
    delete: 'Delete',
    reduction: 'Recovery',
    not_filled: 'Not Filled',
    please_select: 'Please Select',
    search_by_name: 'Search by name',
    personal_information: 'Personal Information',
    exit_system: 'Exit System',
    verification: 'Verification',
    title: 'Title',
    custom: 'Custom',
    select_date: 'Select Date',
    months_1: 'January',
    months_2: 'February',
    months_3: 'March',
    months_4: 'April',
    months_5: 'May',
    months_6: 'June',
    months_7: 'July',
    months_8: 'August',
    months_9: 'September',
    months_10: 'October',
    months_11: 'November',
    months_12: 'December',
    weeks_0: 'Sunday',
    weeks_1: 'Monday',
    weeks_2: 'Tuesday',
    weeks_3: 'Wednesday',
    weeks_4: 'Thursday',
    weeks_5: 'Friday',
    weeks_6: 'Saturday',
    system_parameter_setting: 'System Parameter Setting',
    connection_successful: 'Connection Successful',
    connection_failed: 'Connection Failed',
    save_failed: 'Save Failed',
    host_cannot_be_empty: 'Host cannot be empty',
    port_cannot_be_empty: 'Port cannot be empty',
    account_cannot_be_empty: 'Account cannot be empty',
    remove: 'Remove',
    remove_cancel: 'Remove Cancel',
    remove_success: 'Remove Success',
    tips: 'The authentication information has expired. Please login again.',
    not_performed_yet: 'Not yet implemented',
    incorrect_input: 'Incorrect Input',
    delete_confirm: 'Please enter the following to confirm the deletion:',
    login_username: 'ID or Email',
    input_login_username: 'Please enter user ID or email',
    input_name: 'Please enter user name',
    please_upload: 'Please upload the file',
    please_fill_path: 'Please fill in the URL path',
    formatErr: 'Format Error',
    please_save: 'Please Save',
    reference_documentation: 'Reference documents',
    id: 'ID',
    millisecond: 'Millisecond',
    cannot_be_null: 'Cannot be null',
    required: '{0} is required',
    already_exists: 'Already Exists',
    modifier: 'Modifier',
    validate: 'Validate',
    batch_add: 'Batch Add',
    tag_tip: 'Enter add label',
    table: {
      select_tip: '{0} data selected'
    },
    date: {
      select_date: 'Select Date',
      start_date: 'Start Date',
      end_date: 'End Date',
      select_date_time: 'Select date time',
      start_date_time: 'Start date time',
      end_date_time: 'End date time',
      range_separator: 'to',
      data_time_error: 'The start date cannot be greater than the end date.'
    },
    adv_search: {
      title: 'Advanced search',
      combine: 'Combined query',
      test: 'Test',
      project: 'Project',
      search: 'Search',
      reset: 'Reset',
      and: 'All',
      or: 'Any one',
      operators: {
        is_empty: 'Empty',
        is_not_empty: 'Not empty',
        like: 'Contain',
        not_like: 'Not Contain',
        in: 'In',
        not_in: 'Not in',
        gt: 'Greater Than',
        ge: 'Greater than or equal to',
        lt: 'Less Than',
        le: 'Less than or equal to',
        equals: 'equals',
        not_equals: 'Not equals',
        between: 'between',
        current_user: 'Current User'
      },
      message_box: {
        alert: 'Alert',
        confirm: 'Confirm'
      }
    },
    monitor: 'Monitor',
    image: 'Image',
    tag: 'Tag',
    module: {
      select_module: 'Select Module',
      default_module: 'Default Module'
    },
    datasource: 'Datasource',
    char_can_not_more_50: 'Can not more 50 char',
    share_success: 'Share Success',
    input_id: 'Please input ID',
    input_pwd: 'Please input password',
    message_box: {
      alert: 'Alert',
      confirm: 'Confirm',
      ok: 'Confirm',
      cancel: 'Cancel'
    },
    ukey_title: 'API Keys'
  },
  documentation: {
    documentation: 'Documentation',
    github: 'Github Repository'
  },
  permission: {
    addRole: 'New Role',
    editPermission: 'Edit',
    roles: 'Your roles',
    switchRoles: 'Switch roles',
    tips: 'In some cases, using v-permission will have no effect. For example: Element-UI  el-tab or el-table-column and other scenes that dynamically render dom. You can only do this with v-if.',
    delete: 'Delete',
    confirm: 'Confirm',
    cancel: 'Cancel'
  },
  guide: {
    description: 'The guide page is useful for some people who entered the project for the first time. You can briefly introduce the features of the project. Demo is based on ',
    button: 'Show Guide'
  },
  components: {
    documentation: 'Documentation',
    tinymceTips: 'Rich text is a core feature of the management backend, but at the same time it is a place with lots of pits. In the process of selecting rich texts, I also took a lot of detours. The common rich texts on the market have been basically used, and I finally chose Tinymce. See the more detailed rich text comparison and introduction.',
    dropzoneTips: 'Because my business has special needs, and has to upload images to qiniu, so instead of a third party, I chose encapsulate it by myself. It is very simple, you can see the detail code in @/components/Dropzone.',
    stickyTips: 'when the page is scrolled to the preset position will be sticky on the top.',
    backToTopTips1: 'When the page is scrolled to the specified position, the Back to Top button appears in the lower right corner',
    backToTopTips2: 'You can customize the style of the button, show / hide, height of appearance, height of the return. If you need a text prompt, you can use element-ui el-tooltip elements externally',
    imageUploadTips: 'Since I was using only the vue@1 version, and it is not compatible with mockjs at the moment, I modified it myself, and if you are going to use it, it is better to use official version.'
  },
  table: {
    dynamicTips1: 'Fixed header, sorted by header order',
    dynamicTips2: 'Not fixed header, sorted by click order',
    dragTips1: 'The default order',
    dragTips2: 'The after dragging order',
    title: 'Title',
    importance: 'Imp',
    type: 'Type',
    remark: 'Remark',
    search: 'Search',
    add: 'Add',
    export: 'Export',
    reviewer: 'reviewer',
    id: 'ID',
    date: 'Date',
    author: 'Author',
    readings: 'Readings',
    status: 'Status',
    actions: 'Actions',
    edit: 'Edit',
    publish: 'Publish',
    draft: 'Draft',
    delete: 'Delete',
    cancel: 'Cancel',
    confirm: 'Confirm'
  },
  detabs: {
    eidttitle: 'Edit Title',
    selectview: 'Select View'
  },
  example: {
    warning: 'Creating and editing pages cannot be cached by keep-alive because keep-alive include does not currently support caching based on routes, so it is currently cached based on component name. If you want to achieve a similar caching effect, you can use a browser caching scheme such as localStorage. Or do not use keep-alive include to cache all pages directly. See details'
  },
  errorLog: {
    tips: 'Please click the bug icon in the upper right corner',
    description: 'Now the management system are basically the form of the spa, it enhances the user experience, but it also increases the possibility of page problems, a small negligence may lead to the entire page deadlock. Fortunately Vue provides a way to catch handling exceptions, where you can handle errors or report exceptions.',
    documentation: 'Document introduction'
  },
  excel: {
    export: 'Export',
    selectedExport: 'Export Selected Items',
    placeholder: 'Please enter the file name (default excel-list)'
  },
  zip: {
    export: 'Export',
    placeholder: 'Please enter the file name (default file)'
  },
  pdf: {
    tips: 'Here we use window.print() to implement the feature of downloading PDF.'
  },
  theme: {
    change: 'Change Theme',
    documentation: 'Theme documentation',
    tips: 'Tips: It is different from the theme-pick on the navbar is two different skinning methods, each with different application scenarios. Refer to the documentation for details.',
    base: 'Base color',
    font: 'Font color',
    border: 'Border color',
    background: 'Background color',
    custom: 'Custom color',
    otherSave: 'Theme Save as',
    info: 'Theme info'
  },
  tagsView: {
    refresh: 'Refresh',
    close: 'Close',
    closeOthers: 'Close Others',
    closeAll: 'Close All'
  },
  settings: {
    title: 'Page style setting',
    theme: 'Theme Color',
    tagsView: 'Open Tags-View',
    fixedHeader: 'Fixed Header',
    sidebarLogo: 'Sidebar Logo'
  },
  sysParams: {
    display: 'Display Setting',
    ldap: 'LDAP Setting',
    oidc: 'OIDC Setting'
  },
  license: {
    i18n_no_license_record: 'No License Record',
    i18n_license_is_empty: 'License is empty.',
    title: 'Authorization Management',
    corporation: 'Customer Name',
    time: 'Authorization Time',
    product: 'Product',
    edition: 'Edition',
    licenseVersion: 'Authorized Version',
    count: 'Authorized Count',
    valid_license: 'Valid License',
    show_license: 'Show License',
    valid_license_error: 'Verification failed',
    status: 'Authorization status',
    valid: 'Valid',
    invalid: 'Invalid',
    expired: 'Expired'
  },
  member: {
    create: 'Add members',
    modify: 'Modify members',
    delete_confirm: 'Comfirm to delete this user?',
    please_choose_member: 'Please choose member',
    search_by_name: 'Search by name',
    modify_personal_info: 'Modify personal info',
    edit_password: 'Edit Password',
    edit_information: 'Edit Information',
    input_name: 'Input Name',
    input_email: 'Input Email',
    special_characters_are_not_supported: 'Special characters are not supported',
    mobile_number_format_is_incorrect: 'Incorrect format of mobile phone number',
    email_format_is_incorrect: 'The mailbox format is incorrect',
    password_format_is_incorrect: 'Valid password: 8-30 digits, English upper and lower case letters + numbers + special characters (optional)',
    old_password: 'Old Password',
    new_password: 'New Password',
    repeat_password: 'Confirm Password',
    inconsistent_passwords: 'The two passwords are inconsistent',
    remove_member: 'Confirm to remove the member',
    org_remove_member: 'Removing the user from the organization will remove the permissions of all workspaces in the organization. Confirm to remove the member?',
    input_id_or_email: 'Please enter user ID or user email',
    no_such_user: 'No such user information, please enter the correct user ID or user email.'
  },
  user: {
    create: 'Create User',
    modify: 'Modify User',
    input_name: 'Please enter user name',
    input_id: 'Please enter user ID',
    input_email: 'Please input email',
    input_password: 'Please input a password',
    input_phone: 'Please enter the phone number',
    input_roles: 'Please select role',
    special_characters_are_not_supported: 'Special characters are not supported',
    mobile_number_format_is_incorrect: 'Incorrect format of mobile phone number',
    email_format_is_incorrect: 'The mailbox format is incorrect',
    delete_confirm: 'Confirm to delete this user?',
    apikey_delete_confirm: 'Confirm to delete this API key?',
    input_id_placeholder: 'Please enter ID (Chinese is not supported)',
    source: 'User Source',
    choose_org: 'Choose Organization',
    reset_password: 'Reset Password',
    current_user: 'Current User',
    origin_passwd: 'Origin Password',
    new_passwd: 'New Password',
    confirm_passwd: 'Confirm Password',
    change_password: 'Change Password',
    search_by_name: 'Search by name'
  },
  ldap: {
    url: 'LDAP url',
    dn: 'LDAP DN',
    password: 'Password',
    ou: 'OU',
    filter: 'filter',
    mapping: 'LDAP mapping',
    open: 'Enable LDAP Authentication',
    input_url: 'Please key LDAP url',
    input_dn: 'Please key DN',
    input_password: 'Please key password',
    input_ou: 'Please key OU',
    input_filter: 'Please key filter',
    input_mapping: 'Please key LDAP mapping',
    input_username: 'Please key username',
    input_url_placeholder: 'Please key url (like ldap://localhost:389)',
    input_ou_placeholder: 'Please key OU ',
    input_filter_placeholder: 'Please key filter',
    input_mapping_placeholder: 'like：{"userName":"uid","nickName":"cn","email":"mail"}',
    test_connect: 'Test connect',
    edit: 'Edit',
    login_success: 'Login success',
    url_cannot_be_empty: 'LDAP url can not be empty',
    dn_cannot_be_empty: 'LDAP DN can not be empty',
    ou_cannot_be_empty: 'LDAP OU can not be empty',
    filter_cannot_be_empty: 'LDAP filter can not be empty',
    mapping_cannot_be_empty: 'LDAP mapping can not be empty',
    password_cannot_be_empty: 'LDAP password can not be empty',
    import_ldap: 'Import LDAP User'
  },
  oidc: {
    auth_endpoint: 'Please key AuthEndpoint',
    token_endpoint: 'Please key TokenEndpoint',
    userinfo_endpoint: 'Please key UserinfoEndpoint',
    logout_endpoint: 'Please key logoutEndpoint',
    clientId: 'Please key ClientId',
    secret: 'Please key Secret',
    scope: 'Please key scope',
    redirectUrl: 'Please key redirectUrl',
    open: 'Enable OIDC Authentication'
  },
  role: {
    menu_authorization: 'Menu Authorization',
    data_authorization: 'Data Authorization',
    please_choose_role: 'Please select role',
    admin: 'Administrator',
    org_admin: 'Organization Administrator',
    org_member: 'Organization Member',
    add: 'Create Role',
    delete: 'Delete Role',
    modify: 'Modify Role',
    tips: 'Tips',
    confirm_delete: 'Confirm delete role ',
    role_name: 'Role Name ',
    search_by_name: 'Search by name',
    pls_input_name: 'please input name'
  },
  menu: {
    parent_category: 'Parent Category',
    module_name: 'Module Name',
    module_path: 'Module Path',
    route_addr: 'Route Address',
    menu_sort: 'Menu Sort',
    authority_identification: 'Authority Identification',
    button_name: 'Button Name',
    select_icon: 'Click the select icon',
    create_time: 'Create Time',
    tile: 'Menu Title',
    create: 'Create Menu',
    modify: 'Modify Menu',
    delete: 'Delete Menu',
    delete_confirm: 'Confirm to delete Menu?',
    menu_type: 'Menu Type'
  },
  organization: {
    parent_org: 'Parent Organization',
    select_parent_org: 'Select parent organization',
    top_org: 'Top Organization',
    name: 'Name',
    sort: 'Sort',
    sub_organizations: 'Sub Organizations',
    create_time: 'Create Time',
    create: 'Create',
    modify: 'Modify',
    delete: 'Delete',
    delete_confirm: 'Are you sure you want to delete the organization?',
    input_name: 'Please enter name',
    select_organization: 'Please select organization',
    search_by_name: 'Search by name',
    special_characters_are_not_supported: 'Format error (special characters are not supported and cannot start and end with \'-\')',
    select: 'Select organization'
  },
  system_parameter_setting: {
    mailbox_service_settings: 'Mail Settings',
    test_connection: 'Test connection',
    SMTP_host: 'SMTP Host',
    basic_setting: 'Basic setting',
    front_time_out: 'Request timeOut(unit: second, Attention: Refresh browser takes effect after saving)',
    msg_time_out: 'Message retention time(unit: day)',
    empty_front: 'If empty then default value is 10s',
    empty_msg: 'If empty then default value is 30 days',
    front_error: 'Valid ranger [0 - 100]',
    msg_error: 'Valid ranger [1 - 365]',
    SMTP_port: 'SMTP Port',
    SMTP_account: 'SMTP Account',
    SMTP_password: 'SMTP Password',
    SSL: 'Turn on SSL (if the SMTP port is 465, you usually need to enable SSL)',
    TLS: 'Turn on TLS (if the SMTP port is 587, you usually need to enable TLS)',
    SMTP: 'Secret free SMTP',
    host: 'Host number cannot be empty',
    port: 'Port number cannot be empty',
    account: 'Account cannot be empty',
    test_recipients: 'Test recipients',
    tip: 'Tip: use as test mail recipient only'
  },
  chart: {
    save_snapshot: 'Save Snapshot',
    datalist: 'Chart',
    add_group: 'Add Group',
    add_scene: 'Add Scene',
    group: 'Group',
    scene: 'Scene',
    delete: 'Delete',
    move_to: 'Move',
    rename: 'Rename',
    tips: 'Tips',
    confirm_delete: 'Confirm Delete',
    delete_success: 'Delete Success',
    confirm: 'Confirm',
    cancel: 'Cancel',
    search: 'Search',
    back: 'Back',
    add_table: 'Add Dataset',
    process: 'Speed of progress',
    add_chart: 'Add Chart',
    db_data: 'Database Dataset',
    sql_data: 'SQL data set',
    excel_data: 'Excel data set',
    custom_data: 'Custom data set',
    pls_slc_tbl_left: 'Please select the chart from the left',
    add_db_table: 'Add Database Dataset',
    pls_slc_data_source: 'Please select data source',
    table: 'Table',
    edit: 'Edit',
    create_view: 'Create Chart',
    data_preview: 'Data preview',
    dimension: 'Dimension',
    quota: 'Quota',
    title: 'Title',
    show: 'Show',
    chart_type: 'Chart Type',
    shape_attr: 'Attribute',
    module_style: 'Style',
    result_filter: 'Filters',
    x_axis: 'Horizontal axis',
    y_axis: 'Longitudinal axis',
    chart: 'Chart',
    close: 'Close',
    summary: 'Summary Method',
    fast_calc: 'Fast Calculation',
    sum: 'Sum',
    count: 'Count',
    avg: 'Avg',
    max: 'Max',
    min: 'Min',
    stddev_pop: 'Standard Deviation',
    var_pop: 'Variance',
    quick_calc: 'Fast calculation',
    show_name_set: 'Display name setting',
    color: 'Color',
    color_case: 'Color Scheme',
    pls_slc_color_case: 'Please choose a color scheme',
    color_default: 'Default',
    color_retro: 'Retro',
    color_future: 'Future',
    color_gradual: 'Gradual',
    color_business: 'Business',
    color_gentle: 'Gentle',
    color_elegant: 'Elegant',
    color_technology: 'Technology',
    color_simple: 'Simple',
    not_alpha: 'Opacity',
    size: 'Size',
    bar_width: 'Column Width',
    bar_gap: 'Column septum',
    adapt: 'Self-adaption',
    line_width: 'line_width',
    line_type: 'line_type',
    line_symbol: 'Break point',
    line_symbol_size: 'Break point size',
    line_type_solid: 'Solid line',
    line_type_dashed: 'Dotted line',
    line_symbol_circle: 'circular',
    line_symbol_emptyCircle: 'Hollow circle',
    line_symbol_rect: 'rectangle',
    line_symbol_roundRect: 'Rounded rectangle',
    line_symbol_triangle: 'triangle',
    line_symbol_diamond: 'diamond',
    line_symbol_pin: 'nail',
    line_symbol_arrow: 'arrow',
    line_symbol_none: 'None',
    line_area: 'area',
    pie_inner_radius: 'inner_radius',
    pie_outer_radius: 'outer_radius',
    funnel_width: 'width',
    line_smooth: 'Smooth polyline',
    title_style: 'Title Style',
    text_fontsize: 'font size',
    text_color: 'Font color',
    text_h_position: 'Horizontal position',
    text_v_position: 'Vertical position',
    text_pos_left: 'Left',
    text_pos_center: 'Center',
    text_pos_right: 'Right',
    text_pos_top: 'Top',
    text_pos_bottom: 'Bottom',
    text_italic: 'italic',
    italic: 'italic',
    orient: 'direction',
    horizontal: 'horizontal',
    vertical: 'vertical',
    legend: 'legend',
    shape: 'shape',
    polygon: 'polygon',
    circle: 'circular',
    label: 'label',
    label_position: 'Label location',
    content_formatter: 'Content Format',
    inside: 'Inside',
    tooltip: 'Tips',
    tooltip_item: 'Data Item',
    tooltip_axis: 'Coordinate Axis',
    formatter_plc: 'When the content format is empty, the default format is displayed',
    xAxis: 'Horizontal axis',
    yAxis: 'Longitudinal axis',
    position: 'Position',
    rotate: 'Angle',
    name: 'Name',
    icon: 'Icon',
    trigger_position: 'Trigger Position',
    asc: 'Ascending Order',
    desc: 'Descending Order',
    sort: 'Sort',
    filter: 'Filter',
    none: 'None',
    background: 'Background',

    border: 'Corner',
    border_width: 'Border width',
    border_radius: 'Border radius',
    alpha: 'Transparency',
    add_filter: 'Add Filter',
    no_limit: 'No limit',
    filter_eq: 'Equal',
    filter_not_eq: 'Not Equal',
    filter_lt: 'Less Than',
    filter_le: 'Less than or equal to',
    filter_gt: 'Greater than',
    filter_ge: 'Greater than or equal to',
    filter_null: 'Null',
    filter_not_null: 'Not Null',
    filter_empty: 'Empty String',
    filter_not_empty: 'Not Empty String',
    filter_include: 'Contain',
    filter_not_include: 'Not Contain',
    rose_type: 'Rose pattern',
    radius_mode: 'Radius',
    area_mode: 'Area',
    rose_radius: 'Fillet',
    view_name: 'Chart Title',
    belong_group: 'Belong Group',
    select_group: 'Select Group',
    name_can_not_empty: 'Name cannot be empty',
    template_can_not_empty: 'Please check a Template',
    custom_count: 'Number of records',
    table_title_fontsize: 'Font size of header',
    table_item_fontsize: 'Table font size',
    table_header_bg: 'Header Background',
    table_item_bg: 'Table Background',
    table_item_font_color: 'Font Color',
    stripe: 'Zebra pattern',
    start_angle: 'Start Angle',
    end_angle: 'End Angle',
    style_priority: 'Style Priority',
    dashboard: 'Dashboard',
    dimension_color: 'Name Color',
    quota_color: 'Value Color',
    dimension_font_size: 'Name FontSize',
    quota_font_size: 'Value FontSize',
    space_split: 'Name/Value Space',
    only_one_quota: 'Only support 1 quota',
    only_one_result: 'Only show first result',
    dimension_show: 'Name Show',
    quota_show: 'Value Show',
    title_limit: 'Title cannot be greater than 50 characters',
    filter_condition: 'Filter Condition',
    filter_field_can_null: 'Filter field must choose',
    preview_100_data: 'Preview 100 rows',
    chart_table_normal: 'Summary Table',
    chart_table_info: 'Detail Table',
    chart_card: 'KPI Card',
    chart_bar: 'Base Bar',
    chart_bar_stack: 'Stack Bar',
    chart_bar_horizontal: 'Horizontal Bar',
    chart_bar_stack_horizontal: 'Stack Horizontal Bar',
    chart_line: 'Base Line',
    chart_line_stack: 'Stack Line',
    chart_pie: 'Pie',
    chart_pie_rose: 'Rose Pie',
    chart_funnel: 'Funnel',
    chart_radar: 'Radar',
    chart_gauge: 'Gauge',
    chart_map: 'Map',
    dateStyle: 'Date Style',
    datePattern: 'Date Format',
    y: 'Year',
    y_M: 'Year Month',
    y_M_d: 'Year Month Day',
    H_m_s: 'Hour Minute Second',
    y_M_d_H_m: 'Year Month Day Hour Minute',
    y_M_d_H_m_s: 'Year Month Day Hour Minute Second',
    date_sub: 'yyyy-MM-dd',
    date_split: 'yyyy/MM/dd',
    chartName: 'New Chart',
    chart_show_error: 'can not show normal',
    chart_error_tips: 'Please contact admin ',
    title_cannot_empty: 'Title can not be empty',
    table_title_height: 'Table header height',
    table_item_height: 'Table row height',
    axis_show: 'Axis Show',
    axis_color: 'Axis Color',
    axis_width: 'Axis Width',
    axis_type: 'Axis Type',
    axis_type_solid: 'Solid',
    axis_type_dashed: 'Dashed',
    axis_type_dotted: 'Dotted',
    axis_label_show: 'Label Show',
    axis_label_color: 'Label Color',
    axis_label_fontsize: 'Label Fontsize',
    text_style: 'Font Style',
    bolder: 'Bolder',
    change_ds: 'Change Dataset',
    change_ds_tip: 'Tips：Change Dataset will change fields,you need rebuild chart',
    axis_name_color: 'Name Color',
    axis_name_fontsize: 'Name Fontsize',
    pie_label_line_show: 'Line',
    outside: 'Outside',
    center: 'Center',
    split: 'Axis',
    axis_line: 'Axis Line',
    axis_label: 'Axis Label',
    label_fontsize: 'Label Fontsize',
    split_line: 'Split Line',
    split_color: 'Split Color',
    shadow: 'Shadow',
    condition: 'Filter Value',
    filter_value_can_null: 'Filter value can not empty',
    filter_like: 'Contain',
    filter_not_like: 'Not Contain',
    chart_details: 'Chart Details',
    export_details: 'Export Details',
    color_light: 'Light',
    color_classical: 'Classical',
    color_fresh: 'Fresh',
    color_energy: 'Energy',
    color_red: 'Red',
    color_fast: 'Fast',
    color_spiritual: 'Spiritual',
    chart_data: 'Data',
    chart_style: 'Style',
    drag_block_type_axis: 'Type Axis',
    drag_block_value_axis: 'Value Axis',
    drag_block_table_data_column: 'Data Column',
    drag_block_pie_angel: 'Sector Angle',
    drag_block_pie_label: 'Sector Label',
    drag_block_gauge_angel: 'Pointer Angle',
    drag_block_label_value: 'Value',
    drag_block_funnel_width: 'Funnel Width',
    drag_block_funnel_split: 'Funnel Split',
    drag_block_radar_length: 'Branch Length',
    drag_block_radar_label: 'Branch Label',
    stack_item: 'Stack Item',
    map_range: 'Map range',
    select_map_range: 'Please select map range',
    area: 'Area',
    placeholder_field: 'Drag Field To Here',
    axis_label_rotate: 'Label Rotate',
    chart_scatter_bubble: 'Bubble',
    chart_scatter: 'Scatter',
    bubble_size: 'Bubble Size',
    chart_treemap: 'Tree Map',
    drill: 'Drill',
    drag_block_treemap_label: 'Color Label',
    drag_block_treemap_size: 'Color Size',
    bubble_symbol: 'Shape',
    gap_width: 'Gap Width',
    width: 'Width',
    height: 'Height',
    system_case: 'System',
    custom_case: 'Custom',
    last_layer: 'This Is The Last Layer',
    radar_size: 'Size',
    chart_mix: 'Mix',
    axis_value: 'Axis Value',
    axis_value_min: 'Min',
    axis_value_max: 'Max',
    axis_value_split: 'Split',
    axis_auto: 'Auto',
    table_info_switch: 'Switch detail table will clear dimensions',
    drag_block_value_axis_main: 'Main Axis Value',
    drag_block_value_axis_ext: 'Ext Axis Value',
    yAxis_main: 'Main Vertical Axis',
    yAxis_ext: 'Ext Vertical Axis',
    total: 'Total',
    items: 'Items',
    chart_liquid: 'Liquid',
    drag_block_progress: 'Progress',
    liquid_max: 'End Value',
    liquid_outline_border: 'Border Width',
    liquid_outline_distance: 'Border Distance',
    liquid_wave_length: 'Wave Length',
    liquid_wave_count: 'Wave Count',
    liquid_shape: 'Shape',
    liquid_shape_circle: 'Circle',
    liquid_shape_diamond: 'Diamond',
    liquid_shape_triangle: 'Triangle',
    liquid_shape_pin: 'Pin',
    liquid_shape_rect: 'Rect',
    dimension_or_quota: 'Dimension Or Quota',
    axis_value_split_count: 'Tick Count',
    chart_waterfall: 'Waterfall',
    pie_inner_radius_percent: 'Inner Radius(%)',
    pie_outer_radius_size: 'Outer Radius',
    table_page_size: 'Page Size',
    table_page_size_unit: 'Item/Page',
    result_count: 'Result',
    result_mode_all: 'ALL',
    chart_word_cloud: 'Word Cloud',
    drag_block_word_cloud_label: 'Word Label',
    drag_block_word_cloud_size: 'Word Size',
    splitCount_less_100: 'Split Count Range 0-100',
    change_chart_type: 'Change Type',
    chart_type_table: 'Table',
    chart_type_quota: 'Quota',
    chart_type_trend: 'Trend',
    chart_type_compare: 'Compare',
    chart_type_distribute: 'Distribute',
    chart_type_relation: 'Relation',
    chart_type_space: 'Space',
    preview: 'Preview',
    next: 'Next',
    select_dataset: 'Select Dataset',
    select_chart_type: 'Select Chart Type',
    recover: 'Reset'
  },
  dataset: {
    sheet_warn: 'There are multiple sheet pages, and the first one is extracted by default',
    datalist: 'Data Set',
    name: 'DataSet Name',
    add_group: 'Add Group',
    add_scene: 'Add Scene',
    group: 'Group',
    scene: 'Scene',
    delete: 'Delete',
    move_to: 'Move',
    rename: 'Rename',
    tips: 'Tips',
    confirm_delete: 'confirm_delete',
    delete_success: 'delete_success',
    confirm: 'confirm',
    cancel: 'cancel',
    search: 'search',
    back: 'back',
    add_table: 'Add Table',
    process: 'Speed of progress',
    update: 'update',
    db_data: 'Database Dataset',
    sql_data: 'SQL data set',
    excel_data: 'Excel data set',
    custom_data: 'Custom data set',
    pls_slc_tbl_left: 'Please select the chart from the left',
    add_db_table: 'Add Database Dataset',
    pls_slc_data_source: 'Please select data source',
    table: 'Table',
    edit: 'Edit',
    create_view: 'Create Chart',
    data_preview: 'Data preview',
    field_type: 'Field Type',
    field_name: 'Field Name',
    field_origin_name: 'Field Origin Name',
    field_check: 'Selected',
    update_info: 'Update Info',
    join_view: 'Data Associated',
    text: 'Text',
    time: 'Time',
    value: 'Value',
    mode: 'Mode',
    direct_connect: 'Direct Link',
    sync_data: 'Timing synchronization',
    update_setting: 'Update Setting',
    sync_now: 'Update Now',
    add_task: 'Add Task',
    task_name: 'Task Name',
    task_id: 'Task ID',
    start_time: 'Start Time',
    end_time: 'End Time',
    status: 'State',
    error: 'Error',
    completed: 'Completed',
    underway: 'underway',
    task_update: 'Update Setting',
    update_type: 'Update Type',
    all_scope: 'Full update',
    add_scope: 'Incremental update',
    select_data_time: 'Select date time',
    execute_rate: 'Execution frequency',
    execute_once: 'Execution Now',
    simple_cron: 'Simple repeat',
    cron_config: 'Expression setting',
    no_limit: 'No limit',
    set_end_time: 'Set the end time',
    operate: 'operation',
    save_success: 'Saved Successfully',
    close: 'Close',
    required: 'Required',
    input_content: 'Please input the content',
    add_sql_table: 'Add SQL Dataset',
    preview: 'Preview',
    pls_input_name: 'Please enter a name',
    connect_mode: 'Connection Mode',
    incremental_update_type: 'Incremental update mode',
    incremental_add: 'Incremental Addition',
    incremental_delete: 'Incremental Deletion',
    last_update_time: 'Last update time',
    current_update_time: 'Current update time',
    param: 'Parameter',
    edit_sql: 'Edit SQL Dataset',
    showRow: 'Display line',
    add_excel_table: 'Add excel dataset',
    add_custom_table: 'Add self help dataset',
    upload_file: 'Upload File',
    detail: 'Details',
    type: 'Type',
    create_by: 'Creator',
    create_time: 'Create_time',
    preview_show: 'Display',
    preview_item: 'items data',
    preview_total: 'Total',
    pls_input_less_5: 'Please input integer less 5',
    field_edit: 'Edit Field',
    table_already_add_to: 'This table is already add to',
    uploading: 'Uploading...',
    add_union: 'Create Associations',
    union_setting: 'Association Settings',
    pls_slc_union_field: 'Please select associated field',
    pls_slc_union_table: 'Please select association table',
    source_table: 'Association table',
    source_field: 'Associated fields',
    target_table: 'Associated table',
    target_field: 'Associated field',
    union_relation: 'Relationship',
    pls_setting_union_success: 'Please set the relationship correctly',
    invalid_dataset: 'Kettle is not running, invalid dataset',
    check_all: 'Select all',
    can_not_union_self: 'The associated table cannot be the same as the associated table',
    float: 'Decimal',
    edit_custom_table: 'Edit self help dataset',
    edit_field: 'Edit Field',
    preview_100_data: 'Show 100 lines data',
    invalid_table_check: 'Please sync data first.',
    parse_error: 'Parse failed,please check.Reference：https://dataease.io/docs/faq/dataset_faq/',
    origin_field_type: 'Origin Type',
    edit_excel_table: 'Edit Excel Dataset',
    edit_excel: 'Edit Excel',
    excel_replace: 'Replace',
    excel_add: 'Add',
    dataset_group: 'Dataset Group',
    m1: 'Move ',
    m2: ' To',
    char_can_not_more_50: 'Dataset name can not more 50',
    task_add_title: 'Add Task',
    task_edit_title: 'Edit Task',
    sync_latter: 'Sync latter',
    task: {
      list: 'Task list',
      record: 'Execution record',
      create: 'New task',
      name: 'Task name',
      last_exec_time: 'Last execution time',
      next_exec_time: 'Next execution time',
      last_exec_status: 'Last execution result',
      task_status: 'Task status',
      dataset: 'Data set',
      search_by_name: 'Search by name',
      underway: 'Waiting for execution',
      stopped: 'End',
      pending: 'Pause',
      exec: 'Execute Once',
      confirm_exec: 'Manual trigger execution？',
      change_success: 'State switch successful'
    },
    field_group_type: 'Type',
    location: 'Location',
    left_join: 'LEFT JOIN',
    right_join: 'RIGHT JOIN',
    inner_join: 'INNER JOIN',
    full_join: 'FULL JOIN',
    can_not_union_diff_datasource: 'Union dataset must have same data source',
    operator: 'Operator',
    d_q_trans: 'Dimension/Quota Transform',
    add_calc_field: 'Create calc field',
    input_name: 'Please input name',
    field_exp: 'Field Expression',
    data_type: 'Data Type',
    click_ref_field: 'Click Quote Field',
    click_ref_function: 'Click Quote Function',
    field_manage: 'Field Manage',
    edit_calc_field: 'Edit calc field',
    calc_field: 'Calc Field',
    show_sql: 'Show SQL',
    ple_select_excel: 'Please select excel file to import',
    merge: 'Merge',
    no_merge: 'Dont Merge',
    merge_msg: 'If the fields in the data table are consistent, merge them into one data set?',
    merge_title: 'Merge data',
    field_name_less_50: 'Field name can not more 50 chars.',
    excel_info_1: '1、Merged cells cannot exist in the file；',
    excel_info_2: '2、The first line of the file is the title line, which cannot be empty or date；',
    excel_info_3: '3、The file size shall not exceed 500m。',
    sync_field: 'Sync Field',
    confirm_sync_field: 'Confirm Sync',
    confirm_sync_field_tips: 'Sync field maybe change edit field，please confirm',
    sync_success: 'Success',
    sync_success_1: 'Success，please sync data again'
  },
  datasource: {
    datasource: 'Data Source',
    please_select_left: 'Please select the data source from the left',
    show_info: 'Data Source Info',
    create: 'Create Data Source',
    type: 'Type',
    please_choose_type: 'Please select data source type',
    data_base: 'Database name',
    user_name: 'User Name',
    password: 'Password',
    host: 'Host name / IP address',
    port: 'Port',
    datasource_url: 'URL address',
    please_input_datasource_url: 'Please enter Elasticsearch 地址，e.g: http://es_host:es_port',
    please_input_data_base: 'Please enter the database name',
    please_input_user_name: 'Please enter user name',
    please_input_password: 'Please enter Password',
    please_input_host: 'Please enter host',
    please_input_url: 'Please enter url adress',
    please_input_port: 'Please enter port',
    modify: 'Edit data Source',
    validate_success: 'Verification successful',
    validate: 'Validate',
    search_by_name: 'Search by name',
    delete_warning: 'Confirm to delete?',
    input_name: 'Please input name',
    input_limit_2_25: '2-25 chars',
    input_limit_0_50: '0-50 chars',
    oracle_connection_type: 'Service Name/SID',
    oracle_sid: 'SID',
    oracle_service_name: 'Service Name',
    get_schema: 'Get Schema',
    schema: 'Database Schema',
    please_choose_schema: 'Please select Schema',
    in_valid: 'Invalid datasource',
    initial_pool_size: 'Initial connections',
    min_pool_size: 'Minimum of connections',
    max_pool_size: 'Maximum connection',
    max_idle_time: 'Maximum idle (seconds)',
    acquire_increment: 'Growth number',
    connect_timeout: 'Connection timeout (seconds)',
    please_input_initial_pool_size: 'Please enter the number of initial connections',
    please_input_min_pool_size: 'Please enter the minimum number of connections',
    please_input_max_pool_size: 'Please enter the maximum number of connections',
    please_input_max_idle_time: 'Please enter the maximum idle (seconds)',
    please_input_acquire_increment: 'Please enter the growth number',
    please_input_connect_timeout: 'Please enter the connection timeout (seconds)',
    no_less_then_0: 'Parameters in advanced settings cannot be less than zero',
    port_no_less_then_0: 'Port cannot be less than zero',
    priority: 'Advanced setting',
    extra_params: 'Extra JDBC connection string'
  },
  pblink: {
    key_pwd: 'Please enter the password to open the link',
    input_placeholder: 'Please enter the 4-digit password',
    pwd_error: 'Wrong password',
    pwd_format_error: 'Please enter the 4-digit password',
    sure_bt: 'Confirm'
  },
  panel: {
    no_auth_role: 'Unshared roles',
    auth_role: 'Shared roles',
    picture_limit: 'Only pictures can be inserted',
    drag_here: 'Please drag the left field here',
    copy_link_passwd: 'Copy link and password',
    copy_link: 'Copy link',
    copy_short_link: 'Copy short link',
    copy_short_link_passwd: 'Copy short link and password',
    passwd_protect: 'Password Protect',
    link: 'Link',
    link_share: 'Share Link',
    over_time: 'Over time',
    link_expire: 'Link is expire',
    link_share_desc: 'After opening the link, anyone can access the dashboard through this link.',
    share: 'Share',
    remove_share_confirm: 'Sure removel All share ?',
    share_in: 'Share in',
    share_out: 'Share out',
    who_share: 'Who share',
    when_share: 'When share',
    share_to: 'Share to',
    org: 'Orgnization',
    role: 'Role',
    user: 'User',
    datalist: 'Chart List',
    group: 'Catalogue',
    panel: 'Dashboard',
    groupAdd: 'Create Catalogue',
    panelAdd: 'Create Dashboard',
    delete: 'Delete',
    move_to: 'Move',
    rename: 'Rename',
    import: 'Import',
    tips: 'Tips',
    confirm_delete: 'Confirm Delete',
    delete_success: 'Delete Success',
    confirm: 'Confirm',
    cancel: 'Cancle',
    search: 'Search',
    back: 'Back',
    view: 'Chart',
    edit: 'Edit',
    panel_list: 'Dashboard',
    module: 'Component',
    filter_module: 'Filter Component',
    select_by_module: 'Select by Component',
    sys_template: 'System Template',
    user_template: 'User Template',
    add_category: 'Add Category',
    filter_keywords: 'Enter keywords to filter',
    dashboard_theme: 'Dashboard Theme',
    table: 'Table',
    gap: 'Gap',
    no_gap: 'No Gap',
    component_gap: 'Component Gap',
    refresh_time: 'Refresh Time',
    minute: 'minute',
    second: 'second',
    photo: 'Photo',
    default_panel: 'Default Dashboard',
    create_public_links: 'Create public links',
    to_default: 'Save To Default',
    to_default_panel: 'Save To Default Dashboard',
    store: 'Store',
    save_to_panel: 'Save to template',
    export_to_panel: 'Export to template',
    export_to_pdf: 'Export to PDF',
    preview: 'Preview',
    fullscreen_preview: 'Fullscreen Preview',
    new_tab_preview: 'New Tab Preview',
    select_panel_from_left: 'Please select Dashboard from left',
    template_nale: 'Template name',
    template: 'Template',
    category: 'Category',
    all_org: 'All Organization',
    custom: 'Custom',
    import_template: 'Import Template',
    copy_template: 'Copy Template',
    upload_template: 'Upload Template',
    belong_to_category: 'Category',
    pls_select_belong_to_category: 'Please select category',
    template_name_cannot_be_empty: 'Template name cannot be empty',
    select_by_table: 'Select by table',
    data_list: 'Data list',
    component_list: 'Component list',
    custom_scope: 'Custom control range',
    multiple_choice: 'Multiple choice',
    single_choice: 'Single choice',
    field: 'Field',
    unshared_people: 'Unshared people',
    shared_people: 'Shared people',
    error_data: 'Error getting data, please contact administrator',
    canvas_size: 'Canvas Size',
    canvas_scale: 'Canvas Scale',
    style: 'Style',
    clean_canvas: 'Clean Canvas',
    insert_picture: 'Insert Picture',
    redo: 'Redo',
    undo: 'Undo',
    panelNull: 'This is a Empty Dashboard，You Can Edit and Enrich It',
    copy: 'Copy',
    paste: 'Paste',
    cut: 'Cut',
    lock: 'Lock',
    topComponent: 'Top Component',
    bottomComponent: 'Bottom Component',
    upComponent: 'Up One Level',
    downComponent: 'Down One Level',
    linkage_setting: 'Linkage Setting',
    add_tab: 'Add Tab',
    open_aided_design: 'Open Component Aided Design',
    close_aided_design: 'Close Component Aided Design',
    open_style_design: 'Open Style Design',
    close_style_design: 'Close Style Design',
    matrix_design: 'Matrix Design',
    left: 'X-Axis',
    top: 'Y-Axis',
    height: 'Height',
    width: 'Width',
    color: 'Color',
    backgroundColor: 'BackgroundColor',
    borderStyle: 'Border Style',
    borderWidth: 'BorderWidth',
    borderColor: 'BorderColor',
    borderRadius: 'BorderRadius',
    fontSize: 'FontSize',
    fontWeight: 'FontWeight',
    lineHeight: 'LineHeight',
    letterSpacing: 'LetterSpacing',
    textAlign: 'TextAlign',
    opacity: 'Opacity',
    verticalAlign: 'Vertical Align',
    text_align_left: 'Aline Left',
    text_align_center: 'Aline Center',
    text_align_right: 'Aline Right',
    vertical_align_top: 'Align Align',
    vertical_align_middle: 'Align Middle',
    vertical_align_bottom: 'Align Bottom',
    border_style_solid: 'Solid',
    border_style_dashed: 'Dashed',
    select_component: 'Check Component',
    other_module: 'Other',
    content: 'Content',
    default_panel_name: 'Default Dashboard Name',
    source_panel_name: 'Source Dashboard Name',
    content_style: 'Content Style',
    canvas_self_adaption: 'Canvas Self Adaption',
    panel_save_tips: 'Do you want to save the changes you made to.',
    panel_save_warn_tips: "Your changes will be lost if you don't save them！",
    do_not_save: "Don't Save",
    save_and_close: 'Save',
    drill: 'drill',
    linkage: 'linkage',
    jump: 'Jump',
    cancel_linkage: 'Cancel Linkage',
    remove_all_linkage: 'Remove All Linkage',
    exit_un_march_linkage_field: 'Exit Un March Linkage Field',
    details: 'Details',
    setting: 'Setting',
    no_drill_field: 'Miss relation field',
    matrix: 'matrix',
    suspension: 'suspension',
    new_element_distribution: 'New element Distribution',
    subject_no_edit: 'System Subject Can Not Edit',
    subject_name_not_null: 'Subject Name Can Not Be Null And Less Than 20 charts',
    is_enable: 'Enable',
    open_mode: 'Open Model',
    new_window: 'New Window',
    now_window: 'Now Window',
    hyperLinks: 'hyperlinks',
    link_open_tips: 'Open When Panel Not In Edit Status',
    data_loading: 'Data Loading...',
    export_loading: 'Export Loading...',
    export_pdf: 'Export PDF',
    jump_set: 'Jump Set',
    enable_jump: 'Enable Jump',
    column_name: 'Column Name',
    enable_column: 'Enable Column',
    open_model: 'Open Model',
    link_type: 'Link Type',
    link_outer: 'Outer',
    link_panel: 'panel',
    select_jump_panel: 'Select Jump Panel',
    link_view: 'Link View',
    link_view_field: 'Link View Field',
    add_jump_field: 'Add Jump Field',
    input_jump_link: 'Input Jump Link',
    select_dimension: 'Select Dimension...',
    please_select: 'Please Select',
    video_type: 'Video Type',
    online_video: 'Online Video',
    streaming_media: 'Streaming Media',
    auto_play: 'Auto Play',
    video_tips: 'User Https,Now Format:mp4、webm',
    play_frequency: 'Play Frequency',
    play_once: 'Once',
    play_circle: 'Circle',
    video_links: 'Video Links',
    video_add_tips: 'Please Add Video Info...'
  },
  plugin: {
    local_install: 'Local installation',
    remote_install: 'Remote installation',
    name: 'Plugin name',
    free: 'Free',
    cost: 'Cost',
    descript: 'Descript',
    version: 'Version',
    creator: 'Creator',
    install_time: 'Install Time',
    release_time: 'Time',
    un_install: 'Uninstall',
    uninstall_confirm: 'Comfirm to uninstall the plugin?',
    uninstall_cancel: 'Cancel uninstall plugin'
  },
  display: {
    logo: 'Head system logo',
    loginLogo: 'Login page header logo',
    loginImage: 'Picture on the right side of the login page',
    loginTitle: 'Login page title',
    title: 'System name',
    advice_size: 'Advice picture size',

    themeLight: 'Light',
    themeDark: 'Dark',
    themeCustom: 'Custom'
  },
  auth: {
    authConfig: 'Auth Config',
    authQuickConfig: 'Auth Quick Config',
    dept: 'Dept',
    role: 'Role',
    user: 'User',
    linkAuth: 'Datasource Permissions',
    datasetAuth: 'Dataset Permissions',
    chartAuth: 'Chart Permissions',
    panelAuth: 'Dashboard Permissions',
    menuAuth: 'Menu and operation permission',
    deptHead: 'All Dept',
    roleHead: 'All Role',
    userHead: 'All User',
    linkAuthHead: 'All Datasource',
    datasetAuthHead: 'All Dataset',
    chartAuthHead: 'All Chart',
    panelAuthHead: 'All Chart',
    view: 'View',
    use: 'Use',
    export: 'Export',
    manage: 'Manage'
  },
  about: {
    auth_to: 'Authorized to',
    invalid_license: 'Invalid License',
    update_license: 'Update License',
    expiration_time: 'Expiration Time',
    expirationed: '(Expired)',
    auth_num: 'Authorized quantity',
    version: 'Version',
    version_num: 'Version number',
    standard: 'Standard',
    enterprise: 'Enterprise',
    suport: 'Get technical support',
    update_success: 'Update Success'
  },
  template: {
    exit_same_template_check: 'The Same Name Exists In Now Class. Do You Want To Override It?',
    override: 'Override',
    cancel: 'Cancel',
    confirm_upload: 'Upload Confirm'
  },
  cron: {
    second: 'Second',
    minute: 'Minute',
    hour: 'Hour',
    day: 'Day',
    minute_default: 'Minutes (execution time: 0 seconds)',
    hour_default: 'Hours (execution time: 0 minutes 0 seconds)',
    day_default: 'Day (execution time: 0:00:00)',
    month: 'Month',
    week: 'Week',
    year: 'Year',
    d_w_cant_not_set: 'Day and Week can not same as "Not set"',
    d_w_must_one_set: 'Day and Week at least on as "Not set"',
    every_day: 'Every day',
    cycle: 'Cycle',
    not_set: 'Not set',
    from: 'From',
    to: 'To',
    repeat: 'Repeat',
    day_begin: 'begin,every',
    day_exec: 'execute once',
    work_day: 'weekday',
    this_month: 'This month',
    day_near_work_day: 'nearly weekday',
    this_week_last_day: 'this month last day',
    set: 'Set',
    every_hour: 'Every hour',
    hour_begin: 'begin,every',
    hour_exec: 'execute once',
    every_month: 'Every month',
    month_begin: 'begin,every',
    month_exec: 'execute once',
    every: 'Every',
    every_begin: 'begin,every',
    every_exec: 'execute once',
    every_week: 'Every week',
    week_start: 'From week',
    week_end: 'to week',
    every_year: 'Every year',
    week_tips: 'Tips：1-7 mapping SUN-SAT',
    minute_limit: 'Minutes cannot be less than 1 and greater than 59',
    hour_limit: 'Hours cannot be less than 1 and greater than 23',
    day_limit: 'Days cannot be less than 1 and greater than 31'
  },
  dept: {
    can_not_move_change_sort: 'Cannot move to change sort',
    can_not_move_parent_to_children: 'Parent organization cannot move to its own child node',
    move_success: 'Mobile success',
    name_exist_pre: 'already existed organization named [',
    name_exist_suf: ']',
    root_org: 'Top organization'
  },
  webmsg: {
    web_msg: 'On site message notification',
    show_more: 'View more',
    all_type: 'All type',
    panel_type: 'Panel Share',
    dataset_type: 'Dataset sync',
    content: 'Content',
    sned_time: 'Send Time',
    read_time: 'Read Time',
    type: 'Message Type',
    mark_readed: 'Mark As Read',
    all_mark_readed: 'Mark All As Read',
    please_select: 'Please select at least one message',
    mark_success: 'Mark read successfully',
    receive_manage: 'Receive Manage',
    i18n_msg_type_panel_share: 'Dashboard sharing',
    i18n_msg_type_panel_share_cacnel: 'Dashboard unshared',
    i18n_msg_type_dataset_sync: 'Data set synchronization',
    i18n_msg_type_dataset_sync_success: 'Dataset synchronization successful',
    i18n_msg_type_dataset_sync_faild: 'Dataset synchronization failed',
    i18n_msg_type_all: 'All type',
    i18n_msg_type_ds_invalid: 'Datasource invalid',
    channel_inner_msg: 'On site news'
  },
  denumberrange: {
    label: 'Number range',
    split_placeholder: 'To',
    please_key_min: 'Please key min value',
    please_key_max: 'Please key max value',
    out_of_min: 'The min value cannot be less than the min integer -2³²',
    out_of_max: 'The max value cannot be more than the max integer 2³²-1',
    must_int: 'Please key interger',
    min_out_max: 'The min value must be less than the max value',
    max_out_min: 'The max value must be more than the min value'
  },
  denumberselect: {
    label: 'Number selector',
    placeholder: 'Please select'
  },
  deinputsearch: {
    label: 'Text search',
    placeholder: 'Please key keyword'
  },
  detextselect: {
    label: 'Text selector',
    placeholder: 'Please select'
  },
  detextgridselect: {
    label: 'Text list',
    placeholder: 'Please select'
  },
  denumbergridselect: {
    label: 'Number list',
    placeholder: 'Please select'
  },
  dedaterange: {
    label: 'Date range',
    to_placeholder: 'End date',
    from_placeholder: 'Start date',
    split_placeholder: 'To'
  },
  dedate: {
    label: 'Date',
    placeholder: 'Please select date'
  },
  deyearmonth: {
    label: 'Month',
    placeholder: 'Please select month'
  },
  deyear: {
    label: 'Year',
    placeholder: 'Please select year'
  },
  deshowdate: {
    label: 'Time',
    show_week: 'Show week',
    show_date: 'Show date',
    time_format: 'Time format',
    date_format: 'Date format',
    custom: 'Custom format',
    open_mode: 'Time category',
    m_default: 'Default',
    m_elec: 'Electronic clock',
    m_simple: 'Simple clock',
    m_complex: 'Complex clock',
    select_openMode: 'Please select time category',
    select_time_format: 'Please select time format',
    select_date_format: 'Please select date format'

  }
}
