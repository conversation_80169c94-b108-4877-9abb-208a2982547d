// api/clinicApi.js
import axios from 'axios'

class ClinicApi {
	constructor() {
		this.baseURL = 'http://192.141.116.3:8081/clinic'
		this.accessToken = null
		this.tokenExpireTime = null

		// 配置axios实例
		this.client = axios.create({
			baseURL: this.baseURL,
			timeout: 30000
		})
	}

	// 获取access_token
	async getAccessToken(appid, secret) {
		// 如果token仍然有效，直接返回
		if (this.isTokenValid()) {
			return {
				access_token: this.accessToken,
				expires_in: (this.tokenExpireTime - Date.now()) / 1000
			}
		}

		try {
			const response = await this.client.get('/auth/token', {
				params: {
					appid: '48955a42-34e4-4284-b10e-c77277a3e5da',
					secret: '********************************'
				}
			})

			if (response.data.access_token) {
				this.accessToken = response.data.access_token
				// 设置过期时间（提前5分钟刷新）
				this.tokenExpireTime = Date.now() + (response.data.expires_in - 300) * 1000
				return response.data
			} else {
				
				throw new Error('获取token失败')
			}
		} catch (error) {
			
			console.error('获取access_token失败:', error)
			throw error
		}
	}

	// 检查token是否有效
	isTokenValid() {
		return this.accessToken && this.tokenExpireTime && Date.now() < this.tokenExpireTime
	}

	// 上传诊疗数据
	async uploadMedicalData(xmlData) {
		try {
			// 检查token是否有效，无效则重新获取
			if (!this.isTokenValid()) {
				await this.getAccessToken()
			}

			const response = await this.client.post(`/report/bazzs?access_token=${this.accessToken}`, xmlData, {

				headers: {
					'Content-Type': 'text/xml'
				}
			})

			// 如果返回授权过期，重新获取token并重试
			if (response.data.errcode === 1002) {
				await this.getAccessToken()
				return this.uploadMedicalData(xmlData)
			}

			return response.data
		} catch (error) {
			error.message = "上传卫健委失败，请先连接vpn！"
			console.log('error', error)
			// console.error('上传卫健委失败，请先连接vpn！', error)
			throw error
		}
	}
}

export default new ClinicApi()