<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-16 14:03:21
 * @Description: 中医-数据中台-数据统计-分店数据统计-详情-折线图
 * @type: page
-->

<template>
	<div v-loading="loading" ref="circleCharts" :style="{ width: '100%', height: '100%', zoom: 1 / zoom }"></div>
</template>

<script>

import { zoom } from "@/utils/devicePixelRatio.js";
import echarts from "echarts";

export default {
	name: "EchartComp",
	props: {
		options: {
			type: Object,
		},
	},
	data() {
		return {
			loading: true,
			zoom: 1,
		};
	},
	watch: {
		options: {
			deep: true,
			immediate: true,
			handler() {
				this.loading = true;
				this.handleChartData();
			},
		},
	},
	mounted() {
		this.zoom = zoom();
		this.zoom = zoom() > 1 ? 1 : zoom() < 0.7 ? 1 : zoom();
	},
	beforeDestroy() {
		// 先移除监听器
		if (this.circleCharts) {
			window.removeEventListener('resize', this.circleCharts.resize);
			this.circleCharts.dispose();
			this.circleCharts = null
		}
	},
	methods: {
		handleChartData(options = this.options) {
			if (!this.$refs.circleCharts) {
				this.loading = false;
				return;
			}

			if (!this.circleCharts) {
				this.circleCharts = echarts.init(this.$refs.circleCharts);
				window.addEventListener("resize", this.circleCharts.resize);
			}

			this.circleCharts.setOption(options);

			this.loading = false;
		},
	},
};
</script>

<style></style>