'use strict'
module.exports = {
  NODE_ENV: '"production"',
  BASE_API: '"/api"',
   //BASE_API: '"http://***************:7020/lease-backend-test"',
  // WEB_SOCKET_URL:'"ws://localhost:7020/lease-backend-test/websocket/"',
  // BASE_API: '"http://**************:7020/lease-backend-test"',
  // WEB_SOCKET_URL:'"ws://**************:7020/lease-backend-test/websocket/"',
  // BASE_API: '"http://*************:19020/lease-backend-test"',
  // WEB_SOCKET_URL:'"ws://*************:19020/lease-backend-test/websocket/"',
  // WEB_SOCKET_URL:'"ws://*************:5017/lease-backend-test/websocket/"',
  // BASE_API: '"http://*************:7417/lease-backend-test"',
  // WEB_SOCKET_URL:'"ws://*************:7417/lease-backend-test/websocket/"',

  // 报表地址配置
  // REPORT_CLIENT_URL: '"http://************/dataease/frontend/"',
  // REPORT_SERVER_URL: '"http://************/dataease/backend/"',

  // 文件在线预览地址
  // FILE_PREVIEW_URL: '"http://localhost:7020/lease-backend-test/"',
  // kkFile在线预览地址
  // KK_FILE_URL: '"http://localhost:7018/onlinePreview?url="',
  // FILE_PREVIEW_URL: '"http://*************:19020/lease-backend-test/"',
  // KK_FILE_URL: '"http://*************:17018/onlinePreview?url="',
  // FILE_PREVIEW_URL: '"http://*************:7417/lease-backend-test/"',
  // KK_FILE_URL: '"http://*************:7417/onlinePreview?url="',
  // FILE_PREVIEW_URL: '"http://*************:5017/lease-backend-test/"',
  // KK_FILE_URL: '"http://*************:5017/onlinePreview?url="',

}

