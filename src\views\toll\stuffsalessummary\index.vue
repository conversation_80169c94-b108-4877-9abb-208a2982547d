<template>
<el-card class="page-container" style="padding: 0px;">
   <!-- <el-row>
        <el-col style="text-align:right;margin-bottom:10px">
          <el-button type="primary" icon="el-icon-upload2" @click='exportExcel'>导出</el-button>
        </el-col>
      </el-row> -->
<el-row>
  <!-- <el-col :span="24">
    <div style="margin:0 0 10px 0;">
    <el-radio-group v-model="YpjxcRc.orderby" size="mini" @change="orderbyChange">
      <el-radio-button label="batch_no">批号</el-radio-button>
      <el-radio-button label="code">商品</el-radio-button>
      
    </el-radio-group>
  </div>
  </el-col> -->
   <el-col :span="24">
     <el-form :inline="true" :model="formInline" class="demo-form-inline">
    <el-form-item label="查询时间"  style="margin-right: 3px;">
  <div class="block" style="width:50%">
    <el-date-picker
      v-model="TimeInterval"
      type="datetimerange"
      value-format="yyyy-MM-dd HH:mm:ss"
      :picker-options="pickerOptions"
      :default-time="['00:00:00','23:59:59']"
      range-separator="至"
      start-placeholder="开始日期"
      end-placeholder="结束日期"
      align="right"
      :clearable="false">
    </el-date-picker>
  </div>
    </el-form-item>
    <el-form-item label="类型"  style="margin-right: 3px;">
    <el-select v-model="formInline.lx" placeholder="类型">
      <el-option label="全部" value="qb"></el-option>
      <el-option label="医用材料" value="stuffType_0"></el-option>
      <el-option label="非医用材料" value="stuffType_1"></el-option>
    </el-select>
  </el-form-item>
  <!-- <el-form-item label="状态"  style="margin-right: 3px;">
    <el-select v-model="formInline.zt" placeholder="状态">
      <el-option label="全部" value="qb"></el-option>
      <el-option label="停用" value="0"></el-option>
      <el-option label="启用" value="1"></el-option>
    </el-select>
  </el-form-item> -->

  <!-- <el-form-item label="批号" style="margin-right: 3px;">
    <el-input v-model="formInline.ph" placeholder="请输入批号"></el-input>
  </el-form-item> -->
    <el-form-item label="材料名称" style="margin-right: 3px;">
    <el-input v-model="formInline.clmc" placeholder="请输入材料名称"></el-input>
  </el-form-item>

      <!-- <el-collapse-transition style="margin-right: 5px;">
        <div v-show="show3">
          <el-form-item label="批号" style="margin-right: 5px;">
    <el-input v-model="formInline.ph" placeholder="请输入批号"></el-input>
  </el-form-item>
    <el-form-item label="商品名" style="float:left;margin-right: 5px;">
    <el-input v-model="formInline.spm" placeholder="请输入商品名"></el-input>
  </el-form-item>
        </div>
      </el-collapse-transition> -->
  
  <!-- <el-form-item label="批号">
    <el-input v-model="formInline.ph" placeholder="请输入批号"></el-input>
  </el-form-item>
    <el-form-item label="商品名">
    <el-input v-model="formInline.spm" placeholder="请输入商品名"></el-input>
  </el-form-item> -->
  <el-form-item style="margin-right: 3px;">
    <el-button type="primary" @click="onSubmit">查询</el-button>
    <el-button type="info" icon="el-icon-refresh-left" @click='resetCondition' :plain='true'>重置</el-button>
    <!-- <el-button @click="show3 = !show3">{{show3==true?"收起":"展开"}}</el-button> -->
  </el-form-item>
     </el-form>
   </el-col>
    <el-col :span="24">
  <el-table
    :data="tableData"
    ref="tableData"
    border
    height="calc(100vh - 280px)"
    :summary-method="getSummaries"
    show-summary
   >
    <el-table-column
             label="序号"
              type="index"
              :index="indexMethod"
              align="center">
            </el-table-column>
    <el-table-column
    align="center"
      prop="clbm"
      label="材料编码">
    </el-table-column>
    <el-table-column
      align="center"
      prop="clmc"
      label="材料名称">
    </el-table-column>
    <el-table-column
      align="center"
      prop="cllx"
      label="分类">
    </el-table-column>
    <!-- <el-table-column
    width="150px" 
      prop="txm"
      label="条形码">
    </el-table-column> -->
    <!-- <el-table-column
    width="150px" 
      prop="gyzz"
      label="国药准字">
    </el-table-column> -->
    <el-table-column
      prop="gg"
      align="center"
      label="规格">
    </el-table-column>
    <!-- <el-table-column
      prop="sccj"
      label="生产厂家">
    </el-table-column>
    <el-table-column
    align="center"
      prop="gys"
      label="供应商">
    </el-table-column>
    <el-table-column
    width="150px" 
    align="center"
      prop="lsj"
      label="零售价">
    </el-table-column>
    <el-table-column
    width="150px" 
    align="center"
      prop="cbj"
      label="成本价">
    </el-table-column>
    <el-table-column
    width="120px"
      prop="ph"
      label="批号">
    </el-table-column>
    <el-table-column
    width="200px" 
    align="center"
      prop="yxq"
      label="有效期">
    </el-table-column>
    <el-table-column
    width="150px" 
    align="center"
      prop="qcsl"
      label="期初数量">
    </el-table-column>
    <el-table-column
    width="150px" 
    align="center"
      prop="qccb"
      label="期初成本">
    </el-table-column>
    <el-table-column
    width="150px" 
      align="center"
      prop="bqrk"
      label="本期入库数量">
    </el-table-column>
    <el-table-column
      width="150px" 
      align="center"
      prop="bqrkcb"
      label="本期入库成本">
    </el-table-column>
    <el-table-column
    width="150px" 
      align="center"
      prop="bqcksl"
      label="本期出库数量">
    </el-table-column>
    <el-table-column
    width="150px" 
      align="center"
      prop="bqckcb"
      label="本期出库成本">
    </el-table-column>
    <el-table-column
    width="150px" 
      align="center"
      prop="bqpdsl"
      label="本期盘点数量">
    </el-table-column>
    <el-table-column
    width="150px" 
      align="center"
      prop="bqpdcb"
      label="本期盘点成本">
    </el-table-column> -->
    <el-table-column
    align="center"
      prop="sl"
      label="数量">
    </el-table-column>
    <el-table-column
    align="center"
      prop="zj"
      label="总价">
    </el-table-column>
    
  </el-table>
    </el-col>
</el-row>
<el-row>
        <el-col :span='24'>
          <el-pagination
            background     
            @size-change='onSizeChange'
            @current-change='onCurrentChange'
            :current-page.sync='currentPage'
            :page-sizes='[ 20, 50, 100, patientTotal]'
            :page-size='20'
            layout='total, sizes, prev, pager, next, jumper'
            :total='patientTotal'>
          </el-pagination>
        </el-col>
      </el-row>
</el-card>
</template>

<script>
import { getStuffsalessummarylists,getStuffsalessummarysumss} from "@/api/toll/tollInfo";
import { BigNumber } from "bignumber.js";

  export default {

    data() {
      return {
        show3: false,
              //时间选择
      TimeInterval:this.getInitializeDate(),
      formInline: {
          clmc: '',
          lx:'',
        },

pickerOptions: {
          shortcuts: [{
            text: '最近一周',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '最近一个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit('pick', [start, end]);
            }
          }, {
            text: '最近三个月',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit('pick', [start, end]);
            }
          }]
        },
        
        tableData: [],

        StuffsalessummaryRc: {
        limit: 20,
        offset: 0,
        companyId: currentUser.company.id,
        kssj:"",
        jssj:"",
        lx:"",
        clmc:"",
      },
      currentPage: 1,
      patientTotal: 0,
      patientList: [],
      allTotal:{},

      };
    },
    updated() {
    this.$nextTick(() => {
      this.$refs['tableData'].doLayout();
    })
  },
    methods: {
    
  
       //设置默认日期
    getInitializeDate () {
     let date = new Date()//获取新的时间
     //获取当前年份,并且转为字符串
     let year = date.getFullYear().toString()
     //获取当前月份，因为月份是要从0开始，此处要加1，
    //使用三元表达式，判断是否小于10，如果是的话，就在字符串前面拼接'0'
     let month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1).toString() : (date.getMonth() + 1).toString()
     //获取天，判断是否小于10，如果是的话，就在在字符串前面拼接'0'
     let day = date.getDate() < 10 ? '0' + date.getDate().toString() : date.getDate().toString()
     //字符串拼接，将开始时间和结束时间进行拼接
     let start = year + '-' + month + '-01'+' 00:00:00'    //当月第一天
     //let end=new Date(year, month, 0).getDate();
     let end = year + '-' + month + '-' + day +' 23:59:59'  //当天
     return [start,end] //将值设置给组件DatePicker 绑定的数据
   },
      getSummaries(param) {
        const { columns, data } = param;
        const sums = [];
        columns.forEach((column, index) => {
          if (index === 0) {
            sums[index] = '总价';
            return;
          }
          // else if (index === 8 || index ===9 ||index >11){
          //   const values = data.map(item => Number(item[column.property]));
          // if (!values.every(value => isNaN(value))) {
          //   sums[index] = values.reduce((prev, curr) => {
          //     const value = Number(curr);
          //     if (!isNaN(value)) {
          //       return prev + curr;
          //     } else {
          //       return prev;
          //     }
          //   }, 0);
          //   sums[index] += '';
          // } 
          
          // }
          else {
            sums[index] = '';
          }
          
        });
        // sums[4] = new BigNumber(Number(this.allTotal.sl)).toFormat(2)
        sums[6] = new BigNumber(Number(this.allTotal.zj)).toFormat(2)+'元'
        
        return sums;
      },
       onSizeChange(val) {
      this.currentPage = 1
      this.StuffsalessummaryRc.limit = val;
      this.StuffsalessummaryRc.offset = (this.currentPage - 1) * val
      this.Getypjxclist();
    },
    onCurrentChange(val) {
      this.StuffsalessummaryRc.offset = (val - 1) * this.StuffsalessummaryRc.limit
      this.currentPage = val
      this.Getypjxclist();
    },
    indexMethod(index){
       return (this.currentPage-1)*this.StuffsalessummaryRc.limit+index +1;
    },
     resetCondition(){
      this.StuffsalessummaryRc= {
        limit: 20,
        offset: 0,
        companyId: currentUser.company.id,
        kssj:"",
        jssj:"",
        lx:"",
        clmc:"",
      },
      this.formInline= {
          clmc: '',
          lx:'',
        },
      this.TimeInterval=this.getInitializeDate(),
      this.Getypjxclist();
     },
orderbyChange(){
this.Getypjxclist();
},
    Getypjxclist(){
      this.tableData=[];
  if(this.TimeInterval){
    this.StuffsalessummaryRc.kssj=this.TimeInterval[0];
  this.StuffsalessummaryRc.jssj=this.TimeInterval[1]
  }
//   this.StuffsalessummaryRc.zt=this.formInline.zt=="qb"?"":this.formInline.zt;
 this.StuffsalessummaryRc.lx=this.formInline.lx=="qb"?"":this.formInline.lx;
 this.StuffsalessummaryRc.clmc=this.formInline.clmc;
// this.StuffsalessummaryRc.ph=this.formInline.ph;
// this.StuffsalessummaryRc.orderby=this.StuffsalessummaryRc.orderby;
this.StuffsalessummaryRc.moduleType = window.sessionStorage.getItem("moduleType");
getStuffsalessummarylists(this.StuffsalessummaryRc).then((responseData)=>{
if (responseData.code == 100){
  this.patientTotal=responseData.data.total;
  if(responseData.data.total>0){
responseData.data.rows.forEach((item)=>{
  if(item.cllx=="stuffType_0"){item.cllx="医用材料"}else if(item.cllx=="stuffType_1"){item.cllx="非医用材料"}else{item.cllx=""}
    this.tableData.push({
      clbm:item.clbm,
      clmc:item.clmc,
      gg:item.gg,
      sl:Math.floor(item.sl/item.bzsl) >= 0 ? Math.floor(item.sl/item.bzsl)+(item.bzdw)+((item.sl%item.bzsl > 0) ? (item.sl%item.bzsl) + item.zxdw:""):item.sl+item.zxdw,
      // sl:item.sl,
      zj:item.zj,
      cllx:item.cllx,
    })
  })
  }
  
getStuffsalessummarysumss(this.StuffsalessummaryRc).then((ref)=>{
  if(ref.data.total>0){
    this.allTotal=ref.data.rows[0];
  }
   
})
}

})
    },
    onSubmit() {
        this.Getypjxclist();
      },
    },
    
    mounted(){
      this.Getypjxclist();
    }
  };
</script>
<style lang="scss">
.el-picker-panel__footer .el-picker-panel__link-btn.el-button--text {
  display: none;
}

.el-card__body{
  padding: 0px;
}
</style>