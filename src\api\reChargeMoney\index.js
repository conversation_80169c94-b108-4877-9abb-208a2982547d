/*
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-11 12:09:11
 * @Description: 中医馆-更多-会员管理-充值弹框-api
*/

import request from '@/utils/request'
export default {
	// 确认充值
	reqReChargeManage: data =>
		request({
			url: '/memberPayBill/save',
			method: 'post',
			data
		}),
	// 充值记录详情
	reqChargeRecord: data =>
		request({
			url: '/memberPayBill/list',
			method: 'post',
			data
		}),
	// 消费记录详情
	reqConsumeRecord: data =>
		request({
			url: '/toll/tollInfo/tollTotalForm',
			method: 'post',
			data
		}),
};
