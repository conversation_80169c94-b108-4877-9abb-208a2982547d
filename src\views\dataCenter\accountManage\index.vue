<!--
 * @Author: x<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-16 14:03:21
 * @Description: 中医-数据中台-账户管理
 * @type: page
-->

<template>
	<el-card class="page-container">
		<Form class="demo-form-inline" ref="searchForm" :inline="true" :labelWidth='110' :model="searchForm"
			:items="searchFormItems" :buttons="operationBtns" @search="handleSearch"/>

		<Table class="g-table-container" :tableData="table.tableList" :columns="table.columns" :total="table.total"
			:pagination="table.pagination" :loading="table.isTableLoading" :sizeChange="handleSizeChange"
			:currentChange="handleCurrentChange">
			<el-table-column slot="operate" label="操作" :width="140 + 'px'" fixed="right">
				<template slot-scope='scope'>
					<OperationIcon v-show='permission.view' type='info' content='查看' placement='top-start'
						icon-name='el-icon-view' @click="handleOpenDialog('check', scope.row)"></OperationIcon>
					<OperationIcon v-show='permission.edit' type='primary' content='编辑' placement='top-start'
						icon-name='el-icon-edit' @click="handleOpenDialog('edit', scope.row)"></OperationIcon>
					<OperationIcon v-show='permission.copy' type='primary' content='复制' placement='top-start'
						icon-name='el-icon-document' @click="handleOpenDialog('copy', scope.row)"></OperationIcon>
					<OperationIcon v-show='permission.remove' type='danger' content='删除' placement='top-start'
						icon-name='el-icon-delete' @click='handleDelete(scope.row)'></OperationIcon>
					<OperationIcon v-show='permission.view' type='info' content='历史记录' placement='top-start'
						icon-name='el-icon-info' @click='onShowHistory(scope.$index, scope.row)'></OperationIcon>
				</template>
			</el-table-column>
		</Table>
		<!-- 历史记录  -->
		<History :bussObject='curentRow'></History>
		<!-- 添加编辑查看弹框 -->
		<Dialog ref='clinicForm' :permission='permission' v-on:save-finished='getTableData()'></Dialog>
	</el-card>
</template>
<script>

import ViewColumnsSelect from '@/views/components/ViewColumnsSelect'
import ExportExcelButton from '@/components/ExportExcelButton'
import MainUI from "@/views/components/mainUI";
import Form from "@/components/Combinecom/form";
import Table from "@/components/Combinecom/table";
import API from "@/api/dataCenter";
import OperationIcon from '@/components/OperationIcon'
import History from '@/views/components/history'
import Dialog from './companyDialog'
import { listResourcePermission } from '@/api/admin/common/permission'


export default {
	extends: MainUI,
	name: "accountManage",
	components: { Form, Table, OperationIcon, History, Dialog, ViewColumnsSelect, ExportExcelButton },
	computed: {
		// 查询表单项
		searchFormItems() {
			return [
				{
					type: "text",
					size: "small",
					placeholder: "输入分店名称或客户名称",
					label: "诊所名称",
					value: "name",
					limitLength: 20,
					clearable: true,
				},
			];
		},
		// 查询区按钮配置项
		operationBtns() {
			return [
				{
					class: "is-plain",
					type: "primary",
					size: "small",
					label: "搜索",
					icon: "el-icon-search",
					click: () => this.handleSearch()
				},
				{
					class: "is-plain",
					type: "info",
					size: "small",
					label: "重置",
					icon: "el-icon-refresh-left",
					click: () => this.handleReset(),
				},
				{
					type: "primary",
					size: "small",
					label: "新增分店",
					icon: "el-icon-plus",
					// hidden: () => this.permission.add,
					click: () => this.handleOpenDialog('add')
				},
			];
		},
	},
	data() {
		return {
			// 查询条件栏
			searchForm: {
				name: null,
				params: []
			},
			// 表数据
			table: {
				// table参数
				total: 0,
				tableList: [],
				pagination: {
					limit: 20,
					offset: 0
				},
				columns: [
					{
						label: "序号",
						type: 'index'
					},
					{
						label: "店名",
						prop: "name"
					},
					{
						label: "负责人",
						prop: "contactName"
					},
					{
						label: "地址",
						prop: "address",
						formatter: (row) => {
							return row.addressProvince + row.addressCity + row.addressRegion + row.address
						}
					},
					{
						label: "联系方式",
						prop: "phoneNumber"
					},
					{
						label: "账户信息",
						prop: "bankAccount"
					},
					{
						label: "所属银行",
						prop: "bankBelonging"
					},
					{
						slot: 'operate'
					}
				],
				isTableLoading: false // 表格loading
			},
			// 权限
			permission: {
				view: false,
				add: false,
				edit: false,
				detail: false,
				remove: false,
				copy: false,
			},
			// 表格id请求
			tableId: '*****************',
		};
	},
	mounted() {
		this.handleGetParentId()
	},
	methods: {
		// 关闭
		handleCloseDialog(flag) {
			if (flag) {
				this.getTableData()
			}

		},
		// 删除
		handleDelete(row) {
			this.$confirm("该操作不可撤销，确定删除当前账号吗？", "确定", {
				confirmButtonText: "确定",
				cancelButtonText: "取消",
				type: "warning",
			})
				.then(() => {
					this.getRemoveAPI(row)
				})
				.catch(() => {
					this.$message.info("已取消")
				});
		},
		// 新增\编辑、复制、详情分店
		handleOpenDialog(type, row) {
			if (type == 'add') {
				this.$refs.clinicForm.$emit('openAddClinicDialog', this.searchForm.parentId)
			} else {
				let openType = ""
				if (type === 'edit') {
					openType = 'openEditClinicDialog'
				} else if (type === 'copy') {
					openType = 'openCopyClinicDialog'
				} else if (type === 'check') {
					openType = 'openViewClinicDialog'
				}

				this.setLoad()
				API.reqAccountDetail(row.id).then(res => {
					if (res.code === '100') {
						this.$refs.clinicForm.$emit(openType, res.data)
					} else {
						this.$message.warning(res.data.msg)
					}
					this.resetLoad()
				}).catch(error => {
					this.$message.error(error);
				})
			}
		},
		// total
		handleSizeChange(val) {
			// console.log('val', val)
			this.table.pagination.offset = val;
			this.getTableData();
		},
		// 改变页数
		handleCurrentChange(val) {
			// console.log('val2222', val)
			this.table.pagination.offset = val;
			this.getTableData();
		},
		// 查询
		handleSearch() {
			this.getTableData()
		},
		// 重置
		handleReset() {
			this.searchForm.name = null
			this.getTableData()
		},


		// 初始化调用
		async handleInit() {
			// this.setLoad()
			const data = {
				params: [
					{
						"columnName": "parent_id",
						"queryType": "=",
						"value": this.searchForm.parentId
					},
					{
						"columnName": "name",
						"queryType": "like",
						"value": this.searchForm.name
					},
				],
				limit: this.table.pagination.limit,
				offset: this.table.pagination.offset,
				order: "",
				columnName: ""
			};

			try {
				this.pushDataPermissions(this.searchForm.params, this.$route.meta.routerId, this.tableId)

				let [res, resPermission] = await Promise.all([
					API.reqAccountList(data),
					listResourcePermission(this.$route.meta.routerId)
				])
				if (res.code == 100 && resPermission.code == 100) {

					this.table.tableList = res.data.rows || [];
					this.table.total = Number(res.data.total);
					this.permission.view = resPermission.data.find(item => {
						return item.permission === 'accountManage:read'
					})
					this.permission.export = resPermission.data.find(item => {
						return item.permission === 'accountManage:export'
					})
					this.permission.copy = resPermission.data.find(item => {
						return item.permission === 'accountManage:create'
					})
					this.permission.edit = resPermission.data.find(item => {
						return item.permission === 'accountManage:update'
					})
					this.permission.remove = resPermission.data.find(item => {
						return item.permission === 'accountManage:delete'
					})
				} else {
					this.$message.error(resPermission.code != 100 ? resPermission.msg : res.msg)
				}

				// this.resetLoad()
			} catch (error) {
				this.$message.error(error);
			}
		},

		// 获取诊所parentId
		async handleGetParentId() {
			const id = currentUser.company.id
			try {
				const res = await API.reqAccountParentId(id)
				// console.log('parentId', res)
				if (res.code === '100') {
					this.searchForm.parentId = res.data
					this.handleInit()
				} else {
					this.$message.warning(res.msg)
				}
			} catch (error) {
				this.$message.error(error);
			}
		},
		// 删除
		async getRemoveAPI(row) {

			try {
				const res = await API.reqAccountRemove(row);
				if (res.code === '100') {
					this.$message.success("删除成功!");
					this.getTableData()
				} else {
					this.$message.warning(res.data.msg);
				}
			} catch (error) {
				// this.$message.warning(error);
				this.$message.error(error);
			}

		},
		// table数据
		async getTableData() {
			const data = {
				params: [
					{
						"columnName": "parent_id",
						"queryType": "=",
						"value": this.searchForm.parentId
					},
					{
						"columnName": "name",
						"queryType": "like",
						"value": this.searchForm.name
					},
				],
				limit: this.table.pagination.limit,
				offset: this.table.pagination.offset ? (this.table.pagination.offset - 1) * this.table.pagination.limit : 0,
				order: "",
				columnName: ""
			};
			// console.log('data', data)
			// return
			try {
				this.table.isTableLoading = true;
				const res = await API.reqAccountList(data);
				if (res.code === '100') {
					// 如果当前页无数据且不是第一页，则返回第一页
					if (!res.data.rows && this.table.pagination.offset > 0) {
						this.table.pagination.offset = 0;
						this.getTableData()
					}

					this.table.tableList = res.data.rows || [];
					this.table.total = Number(res.data.total);
				} else {
					this.$message.warning(res.data.msg);
				}
				this.table.isTableLoading = false;
			} catch (error) {
				this.table.isTableLoading = false;
				// this.$message.warning(error);
				this.$message.error(error);
			}
		}
	},
};
</script>

<style lang="scss" scoped>
.tagStyle {
	text-align: center;
}
</style>