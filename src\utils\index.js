
import * as XLSX from 'xlsx';
import XLSXStyle from 'xlsx-style'

/*
 * @Author: xiangfei
 * @Date: 2025-06-16 15:50:15
 * @Description: 中医-全局方法库
*/

export const isNumber = val => typeof val === "number" && !isNaN(val);

export const isBoolean = val => val === true || val === false;

export const isString = val => typeof val === "string";

export const isEmptyString = val => isString(val) && val.trim() === "";

export const isUndefined = val => val === undefined;

export const isNull = val => val === null;

export const isObject = val => {
	if (isNull(val)) {
		return false;
	}
	if (typeof val !== "object") {
		return false;
	}
	if (Object.prototype.toLocaleString.call(val) !== "[object Object]") {
		return false;
	}
	if (Object.getPrototypeOf(val) === null) {
		return true;
	}
	let proto = val;
	while (Object.getPrototypeOf(proto) !== null) {
		proto = Object.getPrototypeOf(proto);
	}
	return Object.getPrototypeOf(val) === proto;
};

export const isEmptyObject = val =>
	isObject(val) && Object.keys(val).length === 0;

export const isArray = val =>
	Object.prototype.toString.call(val) === "[object Array]";

export const isEmptyArray = val => isArray(val) && val.length === 0;

export const isFunction = val => typeof val === "function";

/**
 * @desc 获取对象属性值
 * @param object 对象
 * @param prop 属性，支持链式属性
 */
export const getValueByPath = (object, prop = "") => {
	const paths = prop.split(".");
	let current = object;
	let result = null;
	for (let i = 0, j = paths.length; i < j; i++) {
		const path = paths[i];
		if (!current) {
			break;
		}
		if (i === j - 1) {
			result = current[path];
			break;
		}
		current = current[path];
	}
	return result;
};

/**
 * @desc 函数防抖
 * @param func 函数
 * @param wait 延迟执行毫秒数
 */
export function debounce(func, wait) {
	let timeout = null;
	return function (...args) {
		const context = this;
		timeout && clearTimeout(timeout);
		timeout = setTimeout(() => {
			func.apply(context, args);
		}, wait);
	};
}

/**
 * @desc 函数防抖
 * @param func 函数
 * @param wait 延迟执行毫秒数
 */
export function debounceImmediate(func, wait) {
	let timeout = null;
	return function (...args) {
		const context = this;
		timeout && clearTimeout(timeout);
		const callNow = !timeout;
		timeout = setTimeout(() => {
			timeout = null;
		}, wait);
		callNow && func.apply(context, args);
	};
}

/**
 * @desc 函数节流
 * @param func 函数
 * @param wait 延迟执行毫秒数
 */
export function throttle(func, wait) {
	let previous = 0;
	return function (...args) {
		const context = this;
		const now = Date.now();
		if (now - previous > wait) {
			func.apply(context, args);
			previous = now;
		}
	};
}

/**
 * @desc 函数节流
 * @param func 函数
 * @param wait 延迟执行毫秒数
 */
export function throttleOfTimer(func, wait) {
	let timeout = null;
	return function (...args) {
		const context = this;
		if (!timeout) {
			timeout = setTimeout(() => {
				timeout = null;
				func.apply(context, args);
			}, wait);
		}
	};
}

/**
 * @desc 格式化数字
 * @param num 数字
 * @param max 最大值
 */
export function formatExceedNum(num, max = 1000000) {
	return num <= max ? num : `${max}+`;
}

/**
 * @desc 数组转树
 * @param arr 数组
 * @param id
 * @param parentId 节点父ID字段
 */
export function arrayToTree(arr = [], id = "id", parentId = "parentId") {
	// 构建map
	const map = {};
	for (let i = 0; i < arr.length; i++) {
		const item = arr[i];
		// 构建以id为键 当前数据为值
		map[item[id]] = item;
	}
	const treeList = [];
	for (let i = 0; i < arr.length; i++) {
		const child = arr[i];
		const parent = map[child[parentId]];
		if (parent) {
			// 存在则表示当前数据不是最顶层的数据
			if (!parent.children) {
				parent.children = [];
			}
			parent.children.push(child);
		} else {
			treeList.push(child);
		}
	}
	return treeList;
}

export function findNoChildItem(data, parentId) {
	function findChildren(list, arr) {
		for (let i = 0; i < list.length; i++) {
			const item = list[i];
			if (item.children) {
				findChildren(item.children, arr);
			} else if (item[parentId]) {
				arr.push(item);
			}
		}
	}
	const arr = [];
	findChildren(data, arr);
	return arr;
}

/**
 * @desc 树排序
 * @param tree 树
 * @param key 树节点用来排序的属性
 */
export function sortTree(tree, key) {
	tree.sort((a, b) => a[key] - b[key]);
	for (let i = 0; i < tree.length; i++) {
		const item = tree[i];
		if (item.children && item.children.length) {
			sortTree(item.children, key);
		}
	}
}

/**
 * @desc 过滤字段，只能用于非嵌套对象类型
 * @param target 目标对象
 * @param source 源对象
 * @param persistProps 保留字段
 */
export const filterProps = (target = {}, source = {}, persistProps = []) => {
	const data = {};
	for (let prop in target) {
		if (isObject(target[prop]) || isArray(target[prop])) {
			if (JSON.stringify(target[prop]) !== JSON.stringify(source[prop])) {
				data[prop] = target[prop];
			}
		} else if (target[prop] !== source[prop]) {
			data[prop] = target[prop];
		} else if (persistProps.includes(prop)) {
			data[prop] = target[prop];
		}
	}
	return data;
};

/**
 * @desc 删除空值字段
 * @param obj 对象
 */
export const deleteEmptyProps = obj => {
	for (let prop in obj) {
		if (typeof obj[prop] === "object") {
			deleteEmptyProps(obj[prop]);
		}
		if (
			isUndefined(obj[prop]) ||
			isNull(obj[prop]) ||
			isEmptyString(obj[prop]) ||
			isEmptyObject(obj[prop]) ||
			isEmptyArray(obj[prop])
		) {
			delete obj[prop];
		}
	}
};

/**
 * @desc 百分比计算
 * @param {number} num 分子
 * @param {number} total 分母
 * @returns {number} 返回数百分比
 */
export const percentage = (num, total) => {
	if (num == 0 || total == 0) {
		return 0;
	}
	return Math.round((num / total) * 1000) / 10.0; // 小数点后1位位百分比
};
// 根据身份证号提前年龄 出生日期
export const getIdCardInfo = IdCard => {
	if (IdCard.length !== 18) return;
	const birthday =
		IdCard.substring(6, 10) +
		"-" +
		IdCard.substring(10, 12) +
		"-" +
		IdCard.substring(12, 14);
	//获取年龄
	var month = new Date().getMonth() + 1;
	var day = new Date().getDate();
	var age = new Date().getFullYear() - IdCard.substring(6, 10) - 1;
	if (
		IdCard.substring(10, 12) < month ||
		(IdCard.substring(10, 12) === month && IdCard.substring(12, 14) <= day)
	) {
		age++;
	}
	if (age <= 0) {
		age = 1;
	}
	return { birthday, age };
};
// 查找树形数组
export const findNodeInTree = (treeList, id) => {
	if (!treeList) return;
	let result = null;
	for (const i in treeList) {
		if (result !== null) break;
		const item = treeList[i];
		if (item.value === id) {
			result = item;
			break;
		} else if (item.children && item.children.length) {
			result = findNodeInTree(item.children, id);
		}
	}
	return result;
};

// 脱敏数据 / 身份证identityCard  手机号mobile 姓名name
export const antianaphylaxisData = (type, val) => {
	if (type === "identityCard" && val) {
		let decodeInfo = null
		if (isNumber(Number(val.slice(0, 5)))) {
			decodeInfo = val
		} else {
			decodeInfo = window.atob(val);
		}
		// return decodeInfo.replace(/^(.{3})(?:\d+)(.{3})$/, "$1******$2");
		return decodeInfo.slice(0, 4) + '****' + decodeInfo.slice(decodeInfo.length - 4, decodeInfo.length);
	}
	if (type === "mobile" && val) {
		let decodeInfo;
		if (isNumber(Number(val.slice(0, 3)))) {
			decodeInfo = val;
		} else {
			decodeInfo = window.atob(val);
		}
		return decodeInfo.slice(0, 3) + '****' + decodeInfo.slice(decodeInfo.length - 3, decodeInfo.length);
	}
	if (type === "name") {
		if (val && val != null && val != undefined) {
			let star = "*"; //存放名字中间的*
			//名字是两位的就取姓名首位+*
			if (val.length <= 2) {
				return val.substring(0, 1) + "*";
			} else {
				for (var i = 0; i < val.length - 2; i++) {
					star = star + "*";
				}
				return (
					val.substring(0, 1) +
					star +
					val.substring(val.length - 1, val.length)
				);
			}
		}
	}
};

// 获取支付方式
export const handlePayTypeText = (type) => {
	let val = ""
	switch (type) {
		case "payType_0":
			val = '现金';
			break;
		case "payType_1":
			val = '支付宝';
			break;
		case "payType_2":
			val = '微信';
			break;
		case "payType_3":
			val = '银行卡';
			break;
		case "payType_4":
			val = '医保';
			break;
		case "payType_5":
			val = '会员卡';
			break;
		default:
			val = "";
			break;
	}
	return val;
}

// 下载Excel文档
export const handleDownlaodExcel = (buffer, fileName) => {
	const blob = new Blob([buffer], { type: 'application/octet-stream' });
	const url = URL.createObjectURL(blob);
	const a = document.createElement('a');
	a.href = url;
	a.download = `${fileName}.xlsx`;
	document.body.appendChild(a);
	a.click();
	setTimeout(() => {
		document.body.removeChild(a);
		URL.revokeObjectURL(url);
	}, 100);
}

// 下载生成参数buffer
export const handleChangeBuffer = (s) => {
	if (typeof ArrayBuffer !== "undefined") {
		const buf = new ArrayBuffer(s.length);
		const view = new Uint8Array(buf);
		for (let i = 0; i != s.length; ++i) {
			view[i] = s.charCodeAt(i) & 0xff
		}
		return buf;
	} else {
		const buf = new Array(s.length);
		for (let i = 0; i != s.length; ++i) {
			buf[i] = s.charCodeAt(i) & 0xff;
		}
		return buf;
	}
}



/**
 * 导出Excel文件（带固定表头和居中样式）
 * @param {Array} data 表格数据
 * @param {Array} headers 表头配置 [{label:'姓名', prop:'name'}, ...]
 * @param {String} fileName 文件名(不需要后缀)
 * @param {String} title 主标题（第一行）
 */
export function handleExportExcel(data, headers, title, fileName = 'export') {
	// 1. 创建工作簿
	const wb = XLSX.utils.book_new();
	// 2. 准备数据
	const wsData = [
		[title],
		headers.map(item => item.label).filter(item => item !== undefined).filter(item => item !== '操作').filter(item => item !== '序号'), // 表头行
		...data.map(item => headers.map(header => item[header.prop]).filter(item => item !== undefined)) // 数据行
	];

	headers = headers.filter(item => item.hasOwnProperty('prop'))

	// 3. 创建工作表
	const ws = XLSX.utils.aoa_to_sheet(wsData);

	// 4. 合并标题单元格（跨所有列）
	if (!ws['!merges']) ws['!merges'] = [];
	const colCount = headers.length;

	ws['!merges'].push({
		s: { r: 0, c: 0 },
		e: { r: 0, c: Math.max(0, colCount - 1) } // 结束单元格(行0，最后一列)
	});


	// 5. 定义样式
	const titleStyle = {
		fill: {
			patternType: "solid",
			fgColor: { rgb: "4472C4" } // 蓝色背景
		},
		font: {
			bold: true,
			color: { rgb: "FFFFFF" }, // 白色文字
			sz: 16
		},
		alignment: {
			horizontal: "center",
			vertical: "center",
			wrapText: true
		}
	};

	const headerStyle = {
		fill: {
			patternType: "solid",
			fgColor: { rgb: "D9D9D9" } // 灰色背景
		},
		font: {
			bold: true,
			color: { rgb: "000000" },
			sz: 12
		},
		alignment: {
			horizontal: "center",
			vertical: "center",
			wrapText: true
		},
		border: {
			top: { style: "thin", color: { rgb: "000000" } },
			bottom: { style: "thin", color: { rgb: "000000" } },
			left: { style: "thin", color: { rgb: "000000" } },
			right: { style: "thin", color: { rgb: "000000" } }
		}
	};

	const cellStyle = {
		alignment: {
			horizontal: "center",
			vertical: "center",
			wrapText: true
		},
		border: {
			top: { style: "thin", color: { rgb: "000000" } },
			bottom: { style: "thin", color: { rgb: "000000" } },
			left: { style: "thin", color: { rgb: "000000" } },
			right: { style: "thin", color: { rgb: "000000" } }
		}
	};

	// 6. 应用样式到工作表
	const range = XLSX.utils.decode_range(ws['!ref']);

	// 应用样式到所有单元格
	for (let R = range.s.r; R <= range.e.r; ++R) {
		for (let C = range.s.c; C <= range.e.c; ++C) {
			const cellRef = XLSX.utils.encode_cell({ c: C, r: R });
			if (!ws[cellRef]) ws[cellRef] = { t: 's', v: '' };

			if (R === 0) {
				ws[cellRef].s = titleStyle;
			} else if (R === 1) {
				ws[cellRef].s = headerStyle;
			} else {
				ws[cellRef].s = cellStyle;
			}
		}
	}

	// 7. 设置冻结窗格（固定表头）
	ws['!freeze'] = { xSplit: 0, ySplit: 2, topLeftCell: 'A3', activePane: 'bottomRight' };

	// 8. 设置列宽
	ws['!cols'] = headers.map(header => ({
		wch: header.width || 25, // 默认宽度25
		widthpx: (header.width || 25) * 8 // 近似像素值
	}));

	// 9. 添加工作表到工作簿
	const sheetName = 'Sheet1';
	wb.SheetNames.push(sheetName);
	wb.Sheets[sheetName] = ws;

	// 10. 导出Excel文件
	const wbout = XLSXStyle.write(wb, {
		bookType: 'xlsx',
		type: 'binary',
		bookSST: false
	});

	// 11. 转换为Blob
	const Blob = handleChangeBuffer(wbout)

	// 12、下载
	handleDownlaodExcel(Blob, fileName)

}

/**
		 * @desc 导出Excel
		 * @param res ajax响应
		 * @param filename 导出文件文件名
		 */
export function downloadExcel(res, filename) {
	const blob = new Blob([res.data], {
		type: 'application/octet-stream'
	})
	let url = window.URL.createObjectURL(blob);
	let link = document.createElement('a');
	link.style.display = 'none';
	link.href = url;
	link.setAttribute('download', filename)
	document.body.appendChild(link);
	link.click()
	// const link = document.createElement('a')
	// const blob = new Blob([res.data], { type: 'application/vnd.ms-excel' })
	// // 获取heads中的filename文件名
	// const fileNameEncode = res.headers['content-disposition'] ? res.headers['content-disposition'].split(';')[1].split('filename=')[1] : ''
	// const fileName = filename ? filename : decodeURIComponent(fileNameEncode)
	// link.style.display = 'none'
	// link.href = URL.createObjectURL(blob)
	// link.setAttribute('download', fileName)
	// document.body.appendChild(link)
	// link.click()
	// document.body.removeChild(link)
}