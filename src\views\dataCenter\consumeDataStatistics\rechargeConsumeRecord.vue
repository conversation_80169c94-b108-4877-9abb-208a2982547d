<!--
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-16 14:03:21
 * @Description: 中医-数据中台-数据统计-客户数据统计-充值消费记录弹框
 * @type: page
-->

<template>
	<el-dialog :title='dialogProps.title' :visible.sync='dialogProps.visible' v-if="dialogProps.visible"
		:close-on-click-modal='false' width='60%'>

		<Table class="g-table-container" :tableData="table.tableList" :columns="table.columns" :total="table.total"
			:pagination="table.pagination" :loading="table.isTableLoading" :sizeChange="handleSizeChange"
			:currentChange="handleCurrentChange" />

		<div slot="footer" class='dialog-footer'>
			<el-button @click="dialogProps.visible = false">关 闭</el-button>
		</div>
	</el-dialog>
</template>

<script>

import Table from "@/components/Combinecom/table";
import { handleFormatMoney } from '@/utils/validate'
import API from "@/api/dataCenter";

export default {
	name: "rechargeConsumeRecord",
	components: { Table },
	data() {
		return {
			dialogProps: {
				visible: false,
				action: '',
				title: ''
			},
			// 表数据
			table: {
				// table参数
				total: 0,
				tableList: [],
				pagination: {
					limit: 20,
					offset: 0
				},
				columns: [],
				columns1: [
					{
						label: "序号",
						type: "index",
					},
					{
						label: "费用类型",
						prop: "tollType.name",
					},
					{
						label: "支付方式",
						prop: "paymentType.name",
					},
					{
						label: "实收金额(元)",
						prop: "amountReceived",
						formatter: row => {
							return handleFormatMoney(row.amountReceived)
						}
					},
					{
						label: "优惠金额(元)",
						prop: "amountDiscounted",
						formatter: row => {
							return handleFormatMoney(row.amountDiscounted)
						}
					},
					{
						label: "收费日期",
						prop: "createDate",
					}


				],
				columns2: [
					{
						label: "卡号",
						prop: "card"
					},
					{
						label: "入账金额(元)",
						prop: "money",
						formatter: row => {
							return handleFormatMoney(row.money)
						}
					},
					{
						label: "充值方式",
						prop: "payType.name"
					},
					{
						label: "充值时间",
						prop: "createDate",
					},
				],
				isTableLoading: false // 表格loading
			},
			formData: {}
		};
	},
	mounted() {
		this.$nextTick(() => {
			this.$on('openRecordDialog', function (type, row) {
				this.dialogProps.visible = true
				this.dialogProps.title = type === 1 ? '客户消费记录' : '客户充值记录'
				this.formData = row
				type === 1 ? this.getConsumeRecord() : this.getChargeRecord()
				this.table.columns = type === 1 ? this.table.columns1 : this.table.columns2
			})
		})
	},
	methods: {
		// 计算数字
		handleBigNum(num) {
			if (num || num === '0') {
				return new BigNumber(num).toFormat(2)
			} else {
				return ''
			}
		},
		// total
		handleSizeChange(val) {
			agination.offset = val;
			this.getTableData();
		},
		// 改变页数
		handleCurrentChange(val) {
			agination.offset = val;
			this.getTableData();
		},

		// 获取客户消费信息
		async getConsumeRecord() {
			const data = {
				memberId: this.formData.id,
				companyId: currentUser.company.id,
				// companyId: "2647273530380673139",
				tollType: this.formData.tollType,
				limit: this.table.pagination.limit,
				offset: this.table.pagination.offset,
			}
			// console.log('data', data)
			// return
			try {
				this.table.isTableLoading = true;
				const res = await API.reqConsumeRecord(data);
				if (res.code === '100') {
					// console.log("xiangfei", res.data);
					this.table.tableList = res.data.rows || [];
					this.table.total = Number(res.data.total);
				} else {
					this.$message.warning(res.msg);
				}
				this.table.isTableLoading = false;
			} catch (error) {
				this.table.isTableLoading = false;
				// this.$message.warning(error);
				this.$message.error(error);
			}

		},
		// 获取客户充值信息
		async getChargeRecord() {
			const data = {
				memberId: this.formData.id,
				companyId: currentUser.company.id,
				// companyId: "2647273530380673139",
				tollType: this.formData.tollType,
				limit: this.table.pagination.limit,
				offset: this.table.pagination.offset ? (this.table.pagination.offset - 1) * this.table.pagination.limit : 0,
			}
			// console.log('data', data)
			// return
			try {
				this.table.isTableLoading = true;
				const res = await API.reqConsumeRechargeRecord(data);
				if (res.code === '100') {
					// 如果当前页无数据且不是第一页，则返回第一页
					if (!res.data.rows.length && this.table.pagination.offset > 0) {
						this.table.pagination.offset = 0;
						this.getTableData()
					}
					// console.log("xiangfei", res.data);
					this.table.tableList = res.data.rows || [];
					this.table.total = Number(res.data.total);
				} else {
					this.$message.warning(res.msg);
				}
				this.table.isTableLoading = false;
			} catch (error) {
				this.table.isTableLoading = false;
				// this.$message.warning(error);
				this.$message.error(error);
			}
		},
	},
};
</script>

<style lang="scss" scoped></style>