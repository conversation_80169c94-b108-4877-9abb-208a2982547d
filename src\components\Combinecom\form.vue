<!--
 * @Author: xiang<PERSON>i
 * @Date: 2025-06-16 15:47:34
 * @Description: 中医-全局公共组件-form表单
 * @type: component
-->

<template>
  <el-form
    ref="form"
    v-bind="$attrs"
    :model="model"
    :rules="computedRules"
    :label-width="computedLabelWidth"
    :label-position="$attrs.labelPosition || 'right'"
    :validate-on-rule-change="false"
    v-on="$listeners"
    @keyup.enter.native="handleSearch"
  >
    <slot />
    <!-- 自定义el-form-item -->
    <el-row class="formStyle">
      <el-col :span="24">
        <template v-for="(item, index) in items">
          <slot
            v-if="item.slot && ifRender(item)"
            :name="item.slot"
            :disabled="ifDisabled(item)"
          />
          <el-form-item
            v-if="ifRender(item)"
            :key="item.value"
            :prop="item.type ? item.value : ''"
            v-bind="item"
          >
            <!-- 详情项 -->
            <span v-if="item.type === 'detail'">
              {{ getItemDetail(item) }}
            </span>
            <!-- 普通输入框 -->
            <el-input
              v-if="item.type === 'text' || item.type === 'password'"
              v-model.trim="model[item.value]"
              v-bind="item"
              :disabled="ifDisabled(item)"
              :placeholder="getPlaceholder(item)"
              :maxlength="item.limitLength || ''"
              @focus="isFunction(item.focus) ? item.focus($event) : null"
              @blur="isFunction(item.blur) ? item.blur($event) : null"
            />
            <!-- 文本输入框 -->
            <el-input
              v-if="item.type === 'textarea'"
              v-model.trim="model[item.value]"
              v-bind="item"
              :disabled="ifDisabled(item)"
              :maxlength="item.limitLength || ''"
              :autosize="item.showLine ? item.showLine : false"
              :placeholder="getPlaceholder(item)"
              :show-word-limit="item.showWordLimit"
            />
            <!-- :show-word-limit="showWordLimit?showWordLimit:false" -->
            <!-- 计数器 -->
            <el-input-number
              v-if="item.type === 'inputNumber'"
              v-model="model[item.value]"
              v-bind="item"
              :disabled="ifDisabled(item)"
              :placeholder="getPlaceholder(item)"
            />
            <!-- Switch 开关-->
            <el-switch
              v-if="item.type === 'switch'"
              v-model="model[item.value]"
              v-bind="item"
              :disabled="ifDisabled(item)"
            />
            <!-- 选择框 -->
            <el-select
              v-if="item.type === 'select'"
              v-model="model[item.value]"
              v-bind="item"
              :disabled="ifDisabled(item)"
              :placeholder="getPlaceholder(item)"
            >
              <el-option
                v-for="option in getOptions(item)"
                :key="option.value"
                :value="option.value"
                :label="option.label"
                v-bind="option"
              />
            </el-select>
            <!-- 单选框 -->
            <el-radio-group
              v-if="item.type === 'radio'"
              v-model="model[item.value]"
              v-bind="item"
              :disabled="ifDisabled(item)"
              @change="isFunction(item.change) ? item.change($event) : null"
            >
              <el-radio
                v-for="childItem in getOptions(item)"
                :key="childItem.label"
                v-bind="childItem"
                :label="childItem.value"
              >
                {{ childItem.text || childItem.label }}
              </el-radio>
            </el-radio-group>
            <!-- 复选框 -->
            <el-checkbox-group
              v-if="item.type === 'checkbox'"
              v-model="model[item.value]"
              v-bind="item"
              :disabled="ifDisabled(item)"
              @change="isFunction(item.change) ? item.change() : null"
            >
              <el-checkbox
                v-for="childItem in getOptions(item)"
                :key="childItem.label"
                v-bind="childItem"
              >
                {{ childItem.text || childItem.label }}
              </el-checkbox>
            </el-checkbox-group>
            <!-- 日期选择框 -->
            <el-date-picker
              v-if="
                item.type === 'date' ||
                item.type === 'week' ||
                item.type === 'month' ||
                item.type === 'year'
              "
              v-model="model[item.value]"
              :value-format="item.valueFormat || 'timestamp'"
              v-bind="item"
              :disabled="ifDisabled(item)"
              :placeholder="getPlaceholder(item)"
              @change="
                typeof item.change === 'function'
                  ? item.change($event)
                  : () => {}
              "
            />
            <!-- 日期时间选择框 -->
            <el-date-picker
              v-if="item.type === 'datetime'"
              v-model="model[item.value]"
              :value-format="item.valueFormat || 'timestamp'"
              v-bind="item"
              :disabled="ifDisabled(item)"
              :placeholder="getPlaceholder(item)"
            />
            <!-- 日期时间范围选择框 -->
            <el-date-picker
              v-if="item.type === 'datetimerange'"
              v-model="model[item.value]"
              :value-format="item.valueFormat || 'timestamp'"
              :default-time="item.defaultTime || defaultTime"
              v-bind="item"
              :disabled="ifDisabled(item)"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
            />
            <!-- 日期时间范围不带时分秒选择框 -->
            <el-date-picker
              v-if="item.type === 'daterange'"
              v-model="model[item.value]"
              :value-format="item.valueFormat || 'timestamp'"
              :default-time="item.defaultTime || defaultTime"
              v-bind="item"
              :disabled="ifDisabled(item)"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              class="daterange"
            />
            <!--时间select选择器-->
            <el-time-select
              v-if="item.type === 'timeselect'"
              v-model="model[item.value]"
              :picker-options="item.pickerOptions"
              :disabled="ifDisabled(item)"
              :placeholder="getPlaceholder(item)"
            />
            <!--时间picker选择器-->
            <el-time-picker
              v-if="item.type === 'timepicker'"
              v-model="model[item.value]"
              v-bind="item"
              :disabled="ifDisabled(item)"
              :placeholder="getPlaceholder(item)"
            />
            <!--颜色选择器-->
            <el-color-picker
              v-if="item.type === 'color'"
              v-model="model[item.value]"
              v-bind="item"
            />
            <!--滑块-->
            <el-slider
              v-if="item.type === 'slider'"
              v-model="model[item.value]"
              v-bind="item"
            />
            <!-- 级联选择框 -->
            <el-cascader
              v-if="item.type === 'cascader'"
              v-model="model[item.value]"
              v-bind="item"
              :disabled="ifDisabled(item)"
              @change="isFunction(item.change) ? item.change() : null"
            />
            <!-- 文字链接 -->
            <el-link
              v-if="item.type === 'link'"
              type="primary"
              :href="model[item.value]"
              :underline="false"
              v-bind="item"
              :disabled="ifDisabled(item)"
            >
              {{ model[item.value] }}
            </el-link>
            <!-- 图片 -->
            <el-image
              v-if="item.type === 'image'"
              :class="model[item.value] ? 'img' : ''"
              style="display: block"
              :src="model[item.value]"
              :preview-src-list="[model[item.value]]"
              lazy
              v-bind="item"
              :disabled="ifDisabled(item)"
            >
              <span slot="error">{{ item.errorTip || "无" }}</span>
            </el-image>
            <template v-if="item.icons && item.icons.length">
              <i
                v-for="(icon, iconIndex) in item.icons"
                :key="iconIndex"
                :class="icon.class"
                @click="icon.click"
              />
            </template>
            <template v-if="item.buttons && item.buttons.length">
              <el-button
                v-for="(button, buttonIndex) in getRenderButtons(item)"
                :key="buttonIndex"
                :type="button.type"
                v-bind="button"
                @click="button.click($event, item, index, button, buttonIndex)"
              >
                {{ button.label }}
              </el-button>
            </template>
          </el-form-item>
        </template>

        <el-form-item>
          <Btns :buttons="buttons" />
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
import { getValueByPath, isNumber, isFunction, isBoolean } from "@/utils";
import Btns from "@/components/Combinecom/btns";
export default {
  name: "form",
  inheritAttrs: false,
  props: {
    // 表单数据对象
    model: {
      type: Object,
      default() {
        return {};
      },
    },
    // 表单验证规则
    rules: {
      type: Object,
      default() {
        return {};
      },
    },
    // 表单项
    items: {
      type: Array,
      default: () => [],
    },
    // 表单项标签的宽度
    labelWidth: {
      type: [Number, String],
      default: "80px",
    },
    // 表单项按钮
    buttons: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  components: { Btns },
  data() {
    return {
      defaultTime: ["00:00:00", "23:59:59"],
    };
  },
  computed: {
    computedRules() {
      const formRules = {
        ...this.rules,
      };
      return this.items.reduce((rules, item) => {
        rules[item.value] = rules[item.value] ? rules[item.value] : [];
        if (item.required && !rules[item.value].some((rule) => rule.required)) {
          rules[item.value].push({
            required: true,
            message: "该项为必填项",
          });
        }
        return rules;
      }, formRules);
    },
    computedLabelWidth() {
      if (isNumber(this.labelWidth)) {
        return `${this.labelWidth}px`;
      }
      return this.labelWidth;
    },
  },
  methods: {
    isFunction,
    ifRender(item) {
      let vif = true;
      if (isFunction(item.ifRender)) {
        vif = item.ifRender();
      } else if (isBoolean(item.ifRender)) {
        vif = item.ifRender;
      }
      return vif;
    },
    ifDisabled(item) {
      let disable = false;
      if (isFunction(item.disabled)) {
        disable = item.disabled();
      } else if (isBoolean(item.disabled)) {
        disable = item.disabled;
      }
      return disable;
    },
    getRenderButtons(item) {
      const buttons = [];
      for (let i = 0; i < item.buttons.length; i++) {
        const button = item.buttons[i];
        let render = true;
        if (isFunction(button.ifRender)) {
          render = button.ifRender();
        } else if (isBoolean(button.ifRender)) {
          render = button.ifRender;
        }
        if (render) {
          buttons.push(button);
        }
      }
      return buttons;
    },
    getPlaceholder(item) {
      let placeholder = "";
      if (this.ifDisabled(item)) {
        return;
      }
      if (item.placeholder) {
        return item.placeholder;
      }
      if (
        item.type === "text" ||
        item.type === "textarea" ||
        item.type === "inputNumber"
      ) {
        placeholder = `请输入${item.label}`;
      } else if (
        item.type === "select" ||
        item.type === "data" ||
        item.type === "datetime"
      ) {
        placeholder = `请选择${item.label}`;
      } else {
        placeholder = item.label;
      }
      return placeholder;
    },
    getOptions(item) {
      const newOptions = isFunction(item.options)
        ? item.options()
        : item.options;
      if (item.prop) {
        return newOptions.map((option) => ({
          ...option,
          label: option[item.prop.label || "label"],
          value: option[item.prop.value || "value"],
        }));
      }
      return newOptions;
    },
    getItemDetail(item) {
      const value = getValueByPath(this.model, item.value);
      return isFunction(item.formatter)
        ? item.formatter(item.value, value)
        : value;
    },
    validate(valid) {
      return this.$refs.form.validate(valid);
    },
    validateField(value, callback) {
      return this.$refs.form.validateField(value, callback);
    },
    resetFields() {
      return this.$refs.form.resetFields();
    },
    clearValidate() {
      return this.$refs.form.clearValidate();
    },
    // 触发搜索事件
    handleSearch() {
      this.$emit("search");
    },
  },
};
</script>

<style lang="scss" scoped>
.formStyle {
  margin-left: -40px;

  .el-select {
    width: 100%;
  }

  .img {
    width: 100px;
    height: 100px;
  }

  .btns {
    margin-left: 40px;
  }

  /deep/ .el-textarea .el-input__count {
    bottom: 3px;
    right: 10px;
  }

  /deep/ .el-form-item__label {
    font-weight: 700;
  }
}
</style>
