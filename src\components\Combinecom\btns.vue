<!--
 * @Author: xian<PERSON><PERSON><PERSON>
 * @Date: 2025-06-16 15:47:34
 * @Description: 中医-全局公共组件-按钮
 * @type: component
-->


<template>
	<div class="btns">
		<el-button v-for="button in filterButtons()" :key="button.label" v-bind="button" :disabled="ifDisabled(button)"
			:loading="button.loading" @click="button.click">
			{{ button.label }}
		</el-button>
	</div>
</template>

<script>
import {
	isFunction,
	isBoolean,
} from '@/utils'
export default {
	name: 'ScButtons',
	inheritAttrs: false,
	/* eslint-disable-next-line */
	props: {
		buttons: {
			type: Array,
			required: true,
		},
		/* eslint-disable-next-line */
	},
	methods: {
		filterButtons() {
			return this.buttons.filter(button => this.ifRender(button))
		},
		ifRender(item) {
			let vif = true
			if (isFunction(item.ifRender)) {
				vif = item.ifRender()
			} else if (isBoolean(item.ifRender)) {
				vif = item.ifRender
			}
			return vif
		},
		ifDisabled(item) {
			let disable = false
			if (isFunction(item.disabled)) {
				disable = item.disabled()
			} else if (isBoolean(item.disabled)) {
				disable = item.disabled
			}
			return disable
		},
	},
}
</script>

<style lang="scss" scoped>
.btns {
	line-height: 1;
}

::v-deep .el-button {
	margin-bottom: 10px;
}
</style>
