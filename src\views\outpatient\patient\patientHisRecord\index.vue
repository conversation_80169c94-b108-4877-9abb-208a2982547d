<!--
 * @Author: x<PERSON><PERSON><PERSON><PERSON>
 * @Date: 2025-06-11 10:51:11
 * @Description: 中医馆-客户档案-客户就诊记录
 * @type: page
-->

<template>
	<el-row v-loading='loading'>
		<el-card class="page-container">
			<!--  搜索栏  开始 -->
			<div class='query-form-container'>
				<!-- <OperationIcon type='info' content='返回' placement='top-start' icon-name='el-icon-back' class="back"
					@click="handleBackPatient">
				</OperationIcon> -->
				<el-button size="small" icon="el-icon-back" class="HomeBackBtn" @click="handleBackPatient">返回</el-button>
				<el-row v-if='!moreCodition' class='search-row'>
					<el-form :model='queryModel' @submit.native.prevent label-position="left" label-width='70px' ref='queryForm'
						:inline-message='true'>
						<el-col :span="6">
							<el-form-item label='就诊日期' prop='date'>
								<el-date-picker v-model="queryModel.date" type="date" format="yyyy-MM-dd" placeholder="请选择日期">
								</el-date-picker>
							</el-form-item>
						</el-col>
						<el-col :span="3" style="display:flex;justivy-content:space-around">
							<el-button type="primary" icon="el-icon-search" @click="onSearch()" :plain="true">搜索</el-button>
							<el-button type="info" icon="el-icon-refresh-left" @click="reset" :plain="true">重置</el-button>
						</el-col>
					</el-form>
				</el-row>
				<QueryForm v-else v-model='moreParm' :tableId='tableId' :schemeId='schemeId' :routerId='$route.meta.routerId'
					@search='onSearch()' @moreCodition='onMoreCodition()'></QueryForm>
			</div>
			<!--  搜索栏  结束 -->
			<!-- 表格栏  开始 -->
			<el-row>
				<el-col :span='24'>
					<div @mouseleave='moveTableOutside'>
						<el-table ref="patientTableRef" :data='patientTableList' border @header-dragend='onChangeWidth'
							class="drag_table" :cell-class-name='cellClassName' :header-cell-class-name='headerCellClassName'
							highlight-current-row>
							<el-table-column fixed prop="date" label="日期" :show-overflow-tooltip="true">
							</el-table-column>
							<el-table-column prop="jbxx" :show-overflow-tooltip="true" label="基本信息">
							</el-table-column>
							<!--表行级操作按钮-->
							<el-table-column label='操作' header-align='center' :key="Math.random()" :width='60'>
								<template slot-scope='scope'>
									<el-button @click="handleCheckPatientDetail(scope.row)" type="text" size="small">查看
									</el-button>
								</template>
							</el-table-column>
						</el-table>
					</div>
				</el-col>
			</el-row>
		</el-card>
		<!-- 客户就诊详情 -->
		<PatientDetail ref="patientDetail" />
	</el-row>
</template>

<script>

import { reqParentHisRecord } from '@/api/outpatient/patient'
import ExportExcelButton from '@/components/ExportExcelButton'
import ViewColumnsSelect from '@/views/components/ViewColumnsSelect'
import QueryForm from '@/views/components/queryForm'
import MainUI from '@/views/components/mainUI'
import PatientDetail from '../patientDetail'
import dayjs from 'dayjs'
import OperationIcon from '@/components/OperationIcon'

export default {
	extends: MainUI,
	components: {
		ExportExcelButton,
		ViewColumnsSelect,
		QueryForm,
		PatientDetail,
		OperationIcon
	},
	data() {
		return {
			permission: {
				view: false,
				add: false,
				edit: false,
				detail: false,
				remove: false,
				export: false,
			},
			queryModel: {
				date: "",   // 就诊日期
			},
			search: {
				params: [{ columnName: 'company_id', queryType: '=', value: currentUser.company.id }],
				offset: 0,
				limit: 20,
				columnName: '',      // 排序字段名
				order: ''            // 排序
			},
			currentPage: 1,
			patientTotal: 0,
			patientTableList: [],
			oprColumnWidth: 140,  // 操作列宽
			tableId: '1008489176147648530',
			schemeId: '1008489176147648553',

		}
	},
	methods: {
		// 返回患者列表页
		handleBackPatient() {
			this.$router.push({ path: '/patient' })
		},
		// 查看患者详情
		handleCheckPatientDetail(row) {
			// this.setLoad()
			// console.log('查看患者详情', row)
			this.$refs.patientDetail.$emit('openViewPatientDetailDialog', row)
		},
		// 重置
		reset() {
			this.$refs.queryForm.resetFields()
			this.onSearch()
		},
		// 搜索
		onSearch() {
			if (this.moreCodition) {
				this.search.offset = 0
				this.currentPage = 1
				this.getPatientRecordList()
			} else {
				this.$refs['queryForm'].validate(valid => {
					if (valid) {
						this.search.offset = 0
						this.currentPage = 1
						this.getPatientRecordList()
					} else {
						return false
					}
				})
			}
		},
		// 跳转患者就诊详情
		onDetailPatient(index, row) {
			console.log('跳转患者就诊详情', row)
			// this.$router.push({ path: '/patient/detail' });
			this.$router.push({ path: '/patientHisRecord' })
		},
		// 患者就诊记录
		getPatientRecordList() {
			this.setLoad()
			// /* 查询参数 和数据权限 */
			// this.search.params = [{ columnName: 'company_id', queryType: '=', value: currentUser.company.id }]
			// if (this.moreCodition) {
			// 	this.search.params = this.search.params.concat(this.compositeCondition())
			// } else {
			// 	// 查询参数: 日期
			// 	this.search.params.push({
			// 		columnName: 'date',
			// 		queryType: 'like',
			// 		value: this.queryModel.date
			// 	})
			// }
			// 数据权限: 患者表patient
			// this.pushDataPermissions(this.search.params, this.$route.meta.routerId, this.tableId)
			const params = {
				id: this.$route.query.id,
				date: this.queryModel.date ? dayjs(this.queryModel.date).format('YYYY-MM-DD') : ""
			}

			reqParentHisRecord(params)
				.then((res) => {
					console.log('res', res)
					if (res.code == 100) {
						this.patientTableList = []
						if (!res.data.length) {
							this.resetLoad()
							return
						}
						res.data.forEach((item) => {
							if (item.xyzd != "" && item.xyzd != null) {
								item.xyzd;
							} else {
								item.xyzd = "零售";
							}
							if (item.zyzd != "" && item.zyzd != null) {
								item.zyzd;
							} else {
								item.zyzd = "";
							}
							this.patientTableList.push({
								date: item.jzsj,
								jbxx:
									item.xm +
									" /" +
									item.nl +
									"岁" +
									" /" +
									item.xyzd +
									" /" +
									item.zyzd,
								regid: item.regid,
								patid: item.patid,
							});
						});

					}
					this.resetLoad()
				}).catch(error => {
					this.$message.error(error);
				})
		},
	},
	watch: {
		// tableData是el-table绑定的数据
		patientTableList: {
			// 解决表格显示错位问题
			handler() {
				this.$nextTick(() => {
					// tableRef是el-table绑定的ref属性值
					this.$refs.patientTableRef.doLayout()// 对 Table 进行重新布局
				})
			},
			deep: true
		}
	},
	updated() {
		this.$nextTick(() => {
			// tableRef是el-table绑定的ref属性值
			this.$refs.patientTableRef.doLayout() // 对 Table 进行重新布局
		})
		//}
	},
	mounted() {
		this.getPatientRecordList()
	}
}
</script>
<style lang="scss" scoped>
.page-container {
	padding: 0;
}

.HomeBackBtn {
	position: relative;
	top: -10px;
}


/deep/.el-table {
	.el-table__fixed-body-wrapper {
		top: 47px !important;
	}
}



/deep/ .el-table__fixed-right-patch {
	width: 5px !important
}

/deep/ .el-table colgroup col[name='gutter'] {
	width: 5px !important
}

/deep/ .el-table__body {
	width: 100% !important
}

.drag_table {

	// 设置表格header的高度
	/deep/ th {
		height: 44px;
	}

	/deep/ th.gutter:last-of-type {
		height: 0 !important;
	}

	// 设置表格body的高度
	/deep/.el-table__body-wrapper {
		//解决数据展示超出body高度不滚动bug
		overflow-y: auto;
		// 减去的是表格header的高度
		height: calc(100% - 65px) !important;
	}

	.el-table__fixed-right {
		height: 100% !important;
	}

}
</style>
<style scoped>
/deep/ .el-table__body-wrapper {
	height: calc(100% - 65px) !important;
}
</style>
