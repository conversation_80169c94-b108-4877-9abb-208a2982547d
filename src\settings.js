/**
 * 系统界面设置
 */
module.exports = {
  title: '<PERSON>eke demo',
  /**
   * @type {string} up-down-dark | up-down-light | left-right-dark | left-right-light
   * @description 主题风格 style
   */
  style: 'up-down',
  /**
   * @type {string} #018cb7
   * @description 主题色 theme
   */
  theme: '#018cb7',
  /**
   * @type {string} #018cb7
   * @description header 色
   */
  headerColor: '#018cb7',
  /**
   * @type {string} #2f4050
   * @description header 色
   */
  sidebarColor: '#2f4050',
  /**
   * @type {string}
   * @description 工作区背景色
   */
  backgroundColor: '',
  /**
   * @type {string} default | medium | small | mini
   * @description 布局大小 size
   */
  size: 'mini',
  /**
   * @type {boolean} true | false
   * @description 是否需要 tagsView
   */
  showTagsView: false,
  /**
   * @type {boolean} true | false
   * @description 是否需要固定 header
   */
  fixedHeader: true,
  
  /**
   * @type {boolean} true | false
   * @description 是否显示 logo
   */
  showLogo: true
}
